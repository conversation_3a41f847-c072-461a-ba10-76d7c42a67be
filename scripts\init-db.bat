@echo off
chcp 65001 >nul

echo 🚀 开始初始化数据库...

REM 检查是否安装了 Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
)

REM 生成 Prisma Client
echo 📦 生成 Prisma Client...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ❌ 生成 Prisma Client 失败
    pause
    exit /b 1
)

REM 推送数据库架构
echo 🗄️ 推送数据库架构...
call npx prisma db push
if %errorlevel% neq 0 (
    echo ❌ 推送数据库架构失败
    pause
    exit /b 1
)

REM 运行种子数据
echo 🌱 填充种子数据...
call npx prisma db seed
if %errorlevel% neq 0 (
    echo ❌ 填充种子数据失败
    pause
    exit /b 1
)

echo.
echo ✅ 数据库初始化完成！
echo.
echo 📋 测试账号信息：
echo 管理员: <EMAIL> / 123456
echo 商家: <EMAIL> / 123456
echo 用户: <EMAIL> / 123456
echo.
echo 🎫 已为测试用户创建了优惠券
echo ❤️ 收藏功能已就绪
echo.
echo 🌐 现在可以启动应用: npm run dev
echo.
pause
