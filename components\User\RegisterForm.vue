<template>
  <UForm 
    ref="formRef"
    :schema="registerSchema" 
    :state="formData" 
    @submit="handleSubmit"
    class="space-y-6"
  >
    <!-- 用户名 -->
    <UFormGroup 
      label="用户名" 
      name="username"
      required
      :help="'3-20个字符，只能包含字母、数字和下划线'"
    >
      <UInput
        v-model="formData.username"
        placeholder="请输入用户名"
        size="lg"
        :disabled="isLoading"
        autocomplete="username"
        @blur="validateUsername"
      />
    </UFormGroup>

    <!-- 邮箱 -->
    <UFormGroup 
      label="邮箱地址" 
      name="email"
      required
    >
      <UInput
        v-model="formData.email"
        type="email"
        placeholder="请输入邮箱地址"
        size="lg"
        :disabled="isLoading"
        autocomplete="email"
      />
    </UFormGroup>

    <!-- 手机号 -->
    <UFormGroup 
      label="手机号" 
      name="phone"
      :help="'可选，用于找回密码和接收通知'"
    >
      <UInput
        v-model="formData.phone"
        placeholder="请输入手机号"
        size="lg"
        :disabled="isLoading"
        autocomplete="tel"
      />
    </UFormGroup>

    <!-- 密码 -->
    <UFormGroup 
      label="密码" 
      name="password"
      required
      :help="'至少6个字符，建议包含字母、数字和特殊字符'"
    >
      <UInput
        v-model="formData.password"
        type="password"
        placeholder="请输入密码"
        size="lg"
        :disabled="isLoading"
        autocomplete="new-password"
        @input="checkPasswordStrength"
      />
      
      <!-- 密码强度指示器 -->
      <div v-if="formData.password" class="mt-2">
        <div class="flex items-center space-x-2">
          <div class="flex-1 bg-gray-200 rounded-full h-2">
            <div 
              class="h-2 rounded-full transition-all duration-300"
              :class="passwordStrengthColor"
              :style="{ width: passwordStrengthWidth }"
            />
          </div>
          <span class="text-xs font-medium" :class="passwordStrengthTextColor">
            {{ passwordStrengthText }}
          </span>
        </div>
      </div>
    </UFormGroup>

    <!-- 确认密码 -->
    <UFormGroup 
      label="确认密码" 
      name="confirmPassword"
      required
    >
      <UInput
        v-model="formData.confirmPassword"
        type="password"
        placeholder="请再次输入密码"
        size="lg"
        :disabled="isLoading"
        autocomplete="new-password"
      />
    </UFormGroup>

    <!-- 验证码 -->
    <UFormGroup 
      label="验证码" 
      name="captcha"
      required
    >
      <div class="flex space-x-3">
        <UInput
          v-model="formData.captcha"
          placeholder="请输入验证码"
          size="lg"
          :disabled="isLoading"
          class="flex-1"
        />
        <UButton
          variant="outline"
          size="lg"
          :disabled="isLoading"
          @click="refreshCaptcha"
        >
          <Icon name="heroicons:arrow-path" class="w-4 h-4" />
        </UButton>
      </div>
    </UFormGroup>

    <!-- 服务条款 -->
    <div class="flex items-start space-x-2">
      <UCheckbox
        v-model="formData.agreeToTerms"
        :disabled="isLoading"
        class="mt-1"
      />
      <div class="text-sm text-gray-600">
        我已阅读并同意
        <NuxtLink to="/terms" class="text-primary-600 hover:text-primary-500">
          服务条款
        </NuxtLink>
        和
        <NuxtLink to="/privacy" class="text-primary-600 hover:text-primary-500">
          隐私政策
        </NuxtLink>
      </div>
    </div>

    <!-- 注册按钮 -->
    <UButton
      type="submit"
      size="lg"
      block
      :loading="isLoading"
      :disabled="!isFormValid"
    >
      {{ isLoading ? '注册中...' : '创建账户' }}
    </UButton>
  </UForm>
</template>

<script setup lang="ts">
import { z } from 'zod'
import type { RegisterForm } from '~/types'

// 组件属性
interface Props {
  isLoading?: boolean
}

// 组件事件
interface Emits {
  submit: [data: RegisterForm]
  refreshCaptcha: []
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false
})

const emit = defineEmits<Emits>()

// 表单数据
const formData = reactive<RegisterForm & { agreeToTerms: boolean }>({
  username: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  captcha: '',
  agreeToTerms: false
})

// 表单验证规则
const registerSchema = z.object({
  username: z.string()
    .min(3, '用户名至少3个字符')
    .max(20, '用户名最多20个字符')
    .regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'),
  email: z.string().email('请输入有效的邮箱地址'),
  phone: z.string().regex(/^1[3-9]\d{9}$/, '请输入有效的手机号').optional().or(z.literal('')),
  password: z.string()
    .min(6, '密码至少6个字符')
    .max(50, '密码最多50个字符'),
  confirmPassword: z.string(),
  captcha: z.string().min(4, '请输入验证码'),
  agreeToTerms: z.boolean().refine(val => val === true, '请同意服务条款和隐私政策')
}).refine(data => data.password === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword']
})

// 表单引用
const formRef = ref()

// 密码强度
const passwordStrength = ref(0)
const passwordStrengthText = ref('')
const passwordStrengthColor = ref('bg-gray-300')
const passwordStrengthTextColor = ref('text-gray-500')
const passwordStrengthWidth = ref('0%')

// 计算属性
const isFormValid = computed(() => {
  return formData.username.trim() && 
         formData.email.trim() && 
         formData.password.trim() && 
         formData.confirmPassword.trim() && 
         formData.captcha.trim() &&
         formData.agreeToTerms &&
         formData.password === formData.confirmPassword
})

// 验证用户名
const validateUsername = () => {
  const username = formData.username.trim()
  if (username.length < 3) return
  
  // TODO: 实时检查用户名是否可用
}

// 检查密码强度
const checkPasswordStrength = () => {
  const password = formData.password
  let strength = 0
  
  if (password.length >= 6) strength += 1
  if (password.length >= 8) strength += 1
  if (/[a-z]/.test(password)) strength += 1
  if (/[A-Z]/.test(password)) strength += 1
  if (/[0-9]/.test(password)) strength += 1
  if (/[^a-zA-Z0-9]/.test(password)) strength += 1
  
  passwordStrength.value = strength
  
  if (strength <= 2) {
    passwordStrengthText.value = '弱'
    passwordStrengthColor.value = 'bg-red-500'
    passwordStrengthTextColor.value = 'text-red-500'
    passwordStrengthWidth.value = '33%'
  } else if (strength <= 4) {
    passwordStrengthText.value = '中'
    passwordStrengthColor.value = 'bg-yellow-500'
    passwordStrengthTextColor.value = 'text-yellow-500'
    passwordStrengthWidth.value = '66%'
  } else {
    passwordStrengthText.value = '强'
    passwordStrengthColor.value = 'bg-green-500'
    passwordStrengthTextColor.value = 'text-green-500'
    passwordStrengthWidth.value = '100%'
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  emit('refreshCaptcha')
}

// 处理表单提交
const handleSubmit = () => {
  const { agreeToTerms, ...registerData } = formData
  emit('submit', registerData)
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    username: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    captcha: '',
    agreeToTerms: false
  })
}

// 聚焦到第一个输入框
const focusFirstInput = () => {
  nextTick(() => {
    const firstInput = formRef.value?.$el.querySelector('input')
    if (firstInput) {
      firstInput.focus()
    }
  })
}

// 暴露方法给父组件
defineExpose({
  resetForm,
  focusFirstInput
})

// 组件挂载时聚焦
onMounted(() => {
  focusFirstInput()
})
</script>
