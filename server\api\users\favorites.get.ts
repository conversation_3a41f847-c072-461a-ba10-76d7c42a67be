/**
 * 获取用户收藏列表
 * GET /api/users/favorites
 */

export default defineApiHandler(async (event) => {
  // 获取当前用户ID
  const userId = event.context.user?.id
  if (!userId) {
    throw new AuthenticationError('请先登录')
  }

  const query = getQuery(event)
  
  // 解析分页参数
  const { page, pageSize, skip, take } = parsePaginationQuery(query)
  
  // 解析排序参数
  const allowedSortFields = ['createdAt', 'product.price', 'product.sales']
  const orderBy = parseSortQuery(query, allowedSortFields, 'createdAt', 'desc')
  
  // 构建查询条件
  const where: any = {
    userId,
    product: {
      status: 'ACTIVE' // 只显示上架的商品
    }
  }
  
  // 分类过滤
  if (query.categoryId) {
    where.product.categoryId = parseInt(query.categoryId as string)
  }
  
  // 价格范围过滤
  if (query.minPrice || query.maxPrice) {
    where.product.price = {}
    if (query.minPrice) {
      where.product.price.gte = parseFloat(query.minPrice as string)
    }
    if (query.maxPrice) {
      where.product.price.lte = parseFloat(query.maxPrice as string)
    }
  }

  try {
    // 查询收藏列表和总数
    const [favorites, total] = await Promise.all([
      prisma.favorite.findMany({
        where,
        orderBy: orderBy.length > 0 ? orderBy : [{ createdAt: 'desc' }],
        skip,
        take,
        include: {
          product: {
            include: {
              category: {
                select: {
                  id: true,
                  name: true,
                  slug: true
                }
              },
              merchant: {
                select: {
                  id: true,
                  username: true,
                  nickname: true,
                  avatar: true
                }
              },
              _count: {
                select: {
                  reviews: true,
                  favorites: true
                }
              }
            }
          }
        }
      }),
      prisma.favorite.count({ where })
    ])

    // 格式化收藏数据
    const formattedFavorites = favorites.map(favorite => ({
      id: favorite.id,
      createdAt: favorite.createdAt,
      product: {
        id: favorite.product.id,
        name: favorite.product.name,
        description: favorite.product.description,
        price: favorite.product.price.toNumber(),
        originalPrice: favorite.product.originalPrice?.toNumber() || null,
        images: favorite.product.images,
        stock: favorite.product.stock,
        sales: favorite.product.sales,
        rating: favorite.product.rating?.toNumber() || 0,
        reviewsCount: favorite.product._count.reviews,
        favoritesCount: favorite.product._count.favorites,
        tags: favorite.product.tags,
        status: favorite.product.status,
        createdAt: favorite.product.createdAt,
        updatedAt: favorite.product.updatedAt,
        category: favorite.product.category,
        merchant: favorite.product.merchant
      }
    }))

    // 计算总页数
    const totalPages = Math.ceil(total / pageSize)

    // 获取分类统计
    const categoryStats = await prisma.favorite.groupBy({
      by: ['product'],
      where: { userId },
      _count: true
    })

    const result = {
      items: formattedFavorites,
      total,
      page,
      pageSize,
      totalPages,
      summary: {
        totalFavorites: total,
        categoriesCount: categoryStats.length,
        averagePrice: formattedFavorites.length > 0 
          ? formattedFavorites.reduce((sum, fav) => sum + fav.product.price, 0) / formattedFavorites.length 
          : 0
      }
    }

    return createSuccessResponse(result, '获取收藏列表成功')

  } catch (error) {
    console.error('获取用户收藏失败:', error)
    throw new InternalServerError('获取收藏列表失败')
  }
})
