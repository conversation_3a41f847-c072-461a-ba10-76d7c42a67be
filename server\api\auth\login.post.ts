import bcrypt from 'bcryptjs'
import { z } from 'zod'

/**
 * 用户登录接口
 * POST /api/auth/login
 */

// 请求数据验证schema
const loginSchema = z.object({
  username: z.string().min(1, '用户名不能为空'),
  password: z.string().min(1, '密码不能为空'),
  captcha: z.string().optional(),
  rememberMe: z.boolean().optional()
})

export default defineApiHandler(async event => {
  // 只允许POST请求
  assertMethod(event, 'POST')

  // 获取请求体数据
  const body = await readBody(event)

  // 验证请求数据
  const validatedData = loginSchema.parse(body)

  // 查找用户（支持用户名或邮箱登录）
  const user = await prisma.user.findFirst({
    where: {
      OR: [{ username: validatedData.username }, { email: validatedData.username }]
    }
  })

  // 检查用户是否存在
  if (!user) {
    // 记录登录失败日志
    await logLogin(0, event, {
      loginMethod: 'email',
      success: false,
      failureReason: '用户不存在'
    })
    throw new AuthenticationError('用户名或密码错误')
  }

  // 检查账户状态
  if (user.status !== 'active') {
    // 记录登录失败日志
    await logLogin(user.id, event, {
      loginMethod: 'email',
      success: false,
      failureReason: '账户已被禁用'
    })
    throw new AuthorizationError('账户已被禁用，请联系管理员')
  }

  // 验证密码
  const isPasswordValid = await bcrypt.compare(validatedData.password, user.password)
  if (!isPasswordValid) {
    // 记录登录失败日志
    await logLogin(user.id, event, {
      loginMethod: 'email',
      success: false,
      failureReason: '密码错误'
    })
    throw new AuthenticationError('用户名或密码错误')
  }

  // 生成JWT token
  const expiresIn = validatedData.rememberMe ? '30d' : '7d'
  const token = generateToken(
    {
      id: user.id,
      username: user.username,
      role: user.role
    },
    expiresIn
  )

  // 记录登录成功日志
  await logLogin(user.id, event, {
    loginMethod: 'email',
    success: true
  })

  // 返回用户信息（不包含密码）
  const { password, ...userWithoutPassword } = user

  return createSuccessResponse(
    {
      user: userWithoutPassword,
      token,
      expiresIn
    },
    '登录成功'
  )
})
