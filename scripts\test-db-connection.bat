@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🔗 数据库连接测试工具
echo.

REM 检查.env文件
if not exist ".env" (
    echo ❌ .env 文件不存在
    echo 请先运行 npm run setup:win 或手动创建 .env 文件
    pause
    exit /b 1
)

REM 读取数据库配置
echo 📋 读取数据库配置...

for /f "tokens=2 delims==" %%a in ('findstr "^DATABASE_URL=" .env 2^>nul') do (
    set DATABASE_URL=%%a
    set DATABASE_URL=!DATABASE_URL:"=!
)

if not defined DATABASE_URL (
    echo ❌ DATABASE_URL 未配置
    pause
    exit /b 1
)

echo 数据库URL: !DATABASE_URL!
echo.

REM 简单的URL解析
echo 🔍 连接参数：
echo !DATABASE_URL! | findstr "localhost" >nul
if !errorlevel! equ 0 (
    echo   主机: localhost
    set DB_HOST=localhost
) else (
    echo   主机: 远程服务器
    set DB_HOST=remote
)

echo !DATABASE_URL! | findstr ":5432" >nul
if !errorlevel! equ 0 (
    echo   端口: 5432
    set DB_PORT=5432
) else (
    echo   端口: 其他
    set DB_PORT=other
)

echo !DATABASE_URL! | findstr "postgres" >nul
if !errorlevel! equ 0 (
    echo   用户: postgres
)

echo.

REM 测试网络连通性
echo 🌐 测试网络连通性...

if "%DB_HOST%"=="localhost" if "%DB_PORT%"=="5432" (
    REM 检查本地PostgreSQL服务
    sc query postgresql >nul 2>nul
    if !errorlevel! equ 0 (
        echo ✅ PostgreSQL 服务已安装
        
        sc query postgresql | findstr "RUNNING" >nul
        if !errorlevel! equ 0 (
            echo ✅ PostgreSQL 服务正在运行
        ) else (
            echo ❌ PostgreSQL 服务未运行
            echo 请启动 PostgreSQL 服务
            pause
            exit /b 1
        )
    ) else (
        echo ⚠️  未检测到 PostgreSQL 服务
        echo 请确认 PostgreSQL 已正确安装
    )
    
    REM 测试端口
    netstat -an | findstr ":5432" >nul
    if !errorlevel! equ 0 (
        echo ✅ 端口 5432 正在监听
    ) else (
        echo ❌ 端口 5432 未监听
        echo PostgreSQL 可能未启动或配置错误
    )
) else (
    echo ⚠️  远程数据库连接，跳过本地检查
)

echo.

REM 测试数据库连接
echo 🗄️  测试数据库连接...

REM 检查是否安装了psql
where psql >nul 2>nul
if !errorlevel! equ 0 (
    echo 使用 psql 测试连接...
    
    REM 尝试连接（简化版本，实际密码处理较复杂）
    echo 注意: 由于批处理限制，建议使用 Prisma 测试
) else (
    echo psql 未安装，使用 Prisma 测试连接...
)

REM 使用Prisma测试连接
call npx prisma db push --accept-data-loss >nul 2>nul
if !errorlevel! equ 0 (
    echo ✅ Prisma 数据库连接成功
) else (
    echo ❌ Prisma 数据库连接失败
    echo.
    echo 📋 详细错误信息：
    call npx prisma db push --accept-data-loss 2>&1 | findstr /i "error"
    echo.
    echo 可能的原因：
    echo 1. 数据库服务未启动
    echo 2. 连接信息错误
    echo 3. 用户权限不足
    echo 4. 数据库不存在
    echo.
    pause
    exit /b 1
)

echo.
echo 🎉 数据库连接测试完成！
echo.
echo 📋 后续步骤：
echo 1. 如果连接成功，可以运行: npm run db:init:win
echo 2. 如果连接失败，请检查上述错误信息
echo 3. 需要帮助请查看: docs\CONFIGURATION.md
echo.
pause
