/**
 * 获取搜索日志列表
 * GET /api/admin/logs/search
 */

export default defineApiHandler(async (event) => {
  // 检查管理员权限
  const user = event.context.user
  if (!user || user.role !== 'admin') {
    throw new AuthorizationError('需要管理员权限')
  }

  const query = getQuery(event)
  
  // 解析分页参数
  const { page, pageSize, skip, take } = parsePaginationQuery(query)
  
  // 解析排序参数
  const allowedSortFields = ['createdAt', 'keyword', 'resultCount']
  const orderBy = parseSortQuery(query, allowedSortFields, 'createdAt', 'desc')
  
  // 构建查询条件
  const where: any = {}
  
  // 关键词过滤
  if (query.keyword) {
    where.keyword = {
      contains: query.keyword as string,
      mode: 'insensitive'
    }
  }
  
  // 用户ID过滤
  if (query.userId) {
    where.userId = parseInt(query.userId as string)
  }
  
  // 时间范围过滤
  if (query.startDate || query.endDate) {
    where.createdAt = {}
    if (query.startDate) {
      where.createdAt.gte = new Date(query.startDate as string)
    }
    if (query.endDate) {
      where.createdAt.lte = new Date(query.endDate as string)
    }
  }
  
  // 结果数量范围过滤
  if (query.minResults || query.maxResults) {
    where.resultCount = {}
    if (query.minResults) {
      where.resultCount.gte = parseInt(query.minResults as string)
    }
    if (query.maxResults) {
      where.resultCount.lte = parseInt(query.maxResults as string)
    }
  }

  try {
    // 查询搜索日志和总数
    const [searchLogs, total] = await Promise.all([
      prisma.searchLog.findMany({
        where,
        orderBy,
        skip,
        take,
        include: {
          user: {
            select: {
              id: true,
              username: true,
              nickname: true,
              email: true
            }
          }
        }
      }),
      prisma.searchLog.count({ where })
    ])

    // 计算总页数
    const totalPages = Math.ceil(total / pageSize)

    // 获取统计信息
    const stats = await prisma.searchLog.aggregate({
      where,
      _count: {
        id: true
      },
      _avg: {
        resultCount: true
      },
      _sum: {
        resultCount: true
      }
    })

    // 获取热门搜索关键词
    const popularKeywords = await prisma.searchLog.groupBy({
      by: ['keyword'],
      where: {
        createdAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 最近7天
        }
      },
      _count: {
        keyword: true
      },
      orderBy: {
        _count: {
          keyword: 'desc'
        }
      },
      take: 10
    })

    const result = {
      items: searchLogs,
      total,
      page,
      pageSize,
      totalPages,
      stats: {
        totalSearches: stats._count.id,
        averageResults: stats._avg.resultCount || 0,
        totalResults: stats._sum.resultCount || 0
      },
      popularKeywords: popularKeywords.map(item => ({
        keyword: item.keyword,
        count: item._count.keyword
      }))
    }

    return createSuccessResponse(result, '获取搜索日志成功')

  } catch (error) {
    console.error('获取搜索日志失败:', error)
    throw new InternalServerError('获取搜索日志失败')
  }
})
