<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 搜索结果标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900 mb-2">搜索结果</h1>
      <p v-if="searchResults" class="text-gray-600">
        关键词 "{{ searchQuery }}" 共找到 {{ searchResults.total }} 个商品
      </p>
    </div>

    <!-- 筛选器 -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- 分类筛选 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">商品分类</label>
          <USelect
            v-model="filters.categoryId"
            :options="categoryOptions"
            placeholder="选择分类"
            @change="handleSearch"
          />
        </div>

        <!-- 价格范围 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">价格范围</label>
          <div class="flex space-x-2">
            <UInput v-model="filters.minPrice" type="number" placeholder="最低价" size="sm" @blur="handleSearch" />
            <span class="self-center text-gray-500">-</span>
            <UInput v-model="filters.maxPrice" type="number" placeholder="最高价" size="sm" @blur="handleSearch" />
          </div>
        </div>

        <!-- 排序方式 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">排序方式</label>
          <USelect v-model="sortBy" :options="sortOptions" @change="handleSearch" />
        </div>

        <!-- 其他筛选 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">其他筛选</label>
          <div class="space-y-2">
            <UCheckbox v-model="filters.inStock" label="仅显示有库存" @change="handleSearch" />
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索结果 -->
    <div v-if="isLoading" class="text-center py-12">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      <p class="mt-2 text-gray-600">搜索中...</p>
    </div>

    <div v-else-if="searchResults && searchResults.items.length > 0">
      <!-- 商品网格 -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        <ProductCard
          v-for="product in searchResults.items"
          :key="product.id"
          :product="product"
          @add-to-cart="handleAddToCart"
          @toggle-favorite="handleToggleFavorite"
        />
      </div>

      <!-- 分页 -->
      <div class="flex justify-center">
        <UPagination
          v-model="currentPage"
          :page-count="searchResults.totalPages"
          :total="searchResults.total"
          show-last
          show-first
          @update:model-value="handlePageChange"
        />
      </div>
    </div>

    <!-- 无搜索结果 -->
    <div v-else-if="searchResults && searchResults.items.length === 0" class="text-center py-12">
      <Icon name="heroicons:magnifying-glass" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">未找到相关商品</h3>
      <p class="text-gray-600 mb-4">尝试使用其他关键词或调整筛选条件</p>
      <UButton @click="clearFilters"> 清除筛选条件 </UButton>
    </div>

    <!-- 搜索建议 -->
    <div v-if="searchResults && searchResults.items.length === 0" class="mt-8">
      <h4 class="text-lg font-medium text-gray-900 mb-4">搜索建议</h4>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <UButton
          v-for="suggestion in searchSuggestions"
          :key="suggestion"
          variant="outline"
          size="sm"
          @click="searchWithSuggestion(suggestion)"
        >
          {{ suggestion }}
        </UButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Product } from '~/types'

// 页面元数据
definePageMeta({
  title: '商品搜索'
})

// 状态管理
const authStore = useAuthStore()
const cartStore = useCartStore()
const productsStore = useProductsStore()
const toast = useToast()

// 响应式数据
const route = useRoute()
const router = useRouter()

const searchQuery = ref((route.query.q as string) || '')
const searchResults = ref<any>(null)
const isLoading = ref(false)
const currentPage = ref(1)

// 筛选条件
const filters = reactive({
  categoryId: route.query.categoryId ? parseInt(route.query.categoryId as string) : undefined,
  minPrice: (route.query.minPrice as string) || '',
  maxPrice: (route.query.maxPrice as string) || '',
  inStock: route.query.inStock === 'true'
})

const sortBy = ref((route.query.sortBy as string) || 'createdAt:desc')

// 分类选项
const categoryOptions = ref([
  { label: '全部分类', value: undefined },
  { label: '服装鞋帽', value: 1 },
  { label: '数码电器', value: 2 },
  { label: '家居用品', value: 3 },
  { label: '美妆护肤', value: 4 },
  { label: '运动户外', value: 5 }
])

// 排序选项
const sortOptions = [
  { label: '最新发布', value: 'createdAt:desc' },
  { label: '价格从低到高', value: 'price:asc' },
  { label: '价格从高到低', value: 'price:desc' },
  { label: '销量最高', value: 'sales:desc' },
  { label: '评分最高', value: 'rating:desc' }
]

// 搜索建议
const searchSuggestions = ref<string[]>([])

// 获取搜索建议
const fetchSearchSuggestions = async () => {
  try {
    const response = await $fetch('/api/search/suggestions')
    searchSuggestions.value = response.data.suggestions
  } catch (error) {
    console.error('获取搜索建议失败:', error)
    // 使用默认建议
    searchSuggestions.value = ['手机', '电脑', '衣服', '鞋子', '包包', '化妆品', '家具', '运动装备']
  }
}

// 方法
const handleSearch = async () => {
  if (!searchQuery.value.trim()) return

  try {
    isLoading.value = true

    const [sortField, sortOrder] = sortBy.value.split(':')

    const params = {
      q: searchQuery.value,
      page: currentPage.value,
      pageSize: 20,
      sortBy: sortField,
      sortOrder,
      ...filters
    }

    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === undefined) {
        delete params[key]
      }
    })

    const response = await $fetch('/api/products/search', { params })
    searchResults.value = response.data

    // 更新URL
    await router.push({
      path: '/search',
      query: params
    })
  } catch (error: any) {
    console.error('搜索失败:', error)
    toast.add({
      title: '搜索失败',
      description: error.data?.message || '请稍后重试',
      color: 'red'
    })
  } finally {
    isLoading.value = false
  }
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  handleSearch()
}

const handleAddToCart = async (product: Product) => {
  if (!authStore.isLoggedIn) {
    toast.add({
      title: '请先登录',
      description: '登录后即可添加到购物车',
      color: 'yellow'
    })
    return navigateTo('/login')
  }

  try {
    await cartStore.addItem(product.id, 1)
  } catch (error) {
    console.error('添加到购物车失败:', error)
  }
}

const handleToggleFavorite = async (product: Product) => {
  if (!authStore.isLoggedIn) {
    return navigateTo('/login')
  }

  await productsStore.toggleFavorite(product.id)
}

const clearFilters = () => {
  filters.categoryId = undefined
  filters.minPrice = ''
  filters.maxPrice = ''
  filters.inStock = false
  sortBy.value = 'createdAt:desc'
  currentPage.value = 1
  handleSearch()
}

const searchWithSuggestion = (suggestion: string) => {
  searchQuery.value = suggestion
  clearFilters()
}

// 生命周期
onMounted(async () => {
  // 获取搜索建议
  await fetchSearchSuggestions()

  // 如果有搜索关键词，执行搜索
  if (searchQuery.value) {
    handleSearch()
  }
})

// 监听路由变化
watch(
  () => route.query,
  newQuery => {
    searchQuery.value = (newQuery.q as string) || ''
    if (searchQuery.value) {
      handleSearch()
    }
  }
)
</script>
