import { z } from 'zod'

/**
 * 计算优惠券优惠金额
 * POST /api/coupons/calculate
 */

// 请求数据验证schema
const calculateSchema = z.object({
  userCouponIds: z.array(z.number()).min(1, '请选择至少一张优惠券'),
  orderAmount: z.number().min(0.01, '订单金额必须大于0'),
  items: z.array(z.object({
    productId: z.number(),
    quantity: z.number().min(1),
    price: z.number().min(0)
  })).optional()
})

export default defineApiHandler(async (event) => {
  // 只允许POST请求
  assertMethod(event, 'POST')

  // 获取当前用户ID
  const userId = event.context.user?.id
  if (!userId) {
    throw new AuthenticationError('请先登录')
  }

  // 获取请求体数据
  const body = await readBody(event)

  // 验证请求数据
  const validatedData = calculateSchema.parse(body)

  try {
    // 获取用户选择的优惠券
    const userCoupons = await prisma.userCoupon.findMany({
      where: {
        id: { in: validatedData.userCouponIds },
        userId,
        status: 'UNUSED'
      },
      include: {
        coupon: true
      }
    })

    if (userCoupons.length === 0) {
      throw new BusinessError('未找到可用的优惠券')
    }

    // 检查优惠券有效性
    const now = new Date()
    const validCoupons = userCoupons.filter(userCoupon => {
      const coupon = userCoupon.coupon
      return (
        coupon.status === 'ACTIVE' &&
        now >= coupon.startTime &&
        now <= coupon.endTime &&
        userCoupon.expiresAt >= now
      )
    })

    if (validCoupons.length === 0) {
      throw new BusinessError('所选优惠券均已失效')
    }

    // 检查优惠券类型冲突
    const sharedCoupons = validCoupons.filter(uc => uc.coupon.type === 'SHARED')
    const exclusiveCoupons = validCoupons.filter(uc => uc.coupon.type === 'EXCLUSIVE')

    if (exclusiveCoupons.length > 1) {
      throw new BusinessError('互斥券只能选择一张')
    }

    if (exclusiveCoupons.length > 0 && sharedCoupons.length > 0) {
      throw new BusinessError('选择互斥券时不能同时选择同享券')
    }

    // 获取商品信息（用于检查适用范围）
    let productIds: number[] = []
    let categoryIds: number[] = []
    let merchantIds: number[] = []

    if (validatedData.items && validatedData.items.length > 0) {
      productIds = validatedData.items.map(item => item.productId)
      
      const products = await prisma.product.findMany({
        where: { id: { in: productIds } },
        select: { id: true, categoryId: true, merchantId: true }
      })
      
      categoryIds = [...new Set(products.map(p => p.categoryId))]
      merchantIds = [...new Set(products.map(p => p.merchantId))]
    }

    // 计算优惠金额
    let totalDiscount = 0
    const couponDiscounts: Array<{
      userCouponId: number
      couponId: number
      name: string
      type: string
      discountAmount: number
      applicable: boolean
      reason?: string
    }> = []

    for (const userCoupon of validCoupons) {
      const coupon = userCoupon.coupon
      let discountAmount = 0
      let applicable = true
      let reason = ''

      // 检查最低订单金额
      const minOrderAmount = coupon.minOrderAmount?.toNumber() || 0
      if (validatedData.orderAmount < minOrderAmount) {
        applicable = false
        reason = `订单金额需满${minOrderAmount}元`
      } else {
        // 检查适用范围
        if (coupon.applicableType === 'PRODUCT' && productIds.length > 0) {
          const hasApplicableProduct = productIds.some(id => coupon.applicableIds.includes(id))
          if (!hasApplicableProduct) {
            applicable = false
            reason = '当前商品不适用此优惠券'
          }
        } else if (coupon.applicableType === 'CATEGORY' && categoryIds.length > 0) {
          const hasApplicableCategory = categoryIds.some(id => coupon.applicableIds.includes(id))
          if (!hasApplicableCategory) {
            applicable = false
            reason = '当前商品分类不适用此优惠券'
          }
        } else if (coupon.applicableType === 'MERCHANT' && merchantIds.length > 0) {
          const hasApplicableMerchant = merchantIds.some(id => coupon.applicableIds.includes(id))
          if (!hasApplicableMerchant) {
            applicable = false
            reason = '当前商家不适用此优惠券'
          }
        }

        // 计算优惠金额
        if (applicable) {
          if (coupon.discountType === 'FIXED') {
            discountAmount = coupon.discountValue.toNumber()
          } else if (coupon.discountType === 'PERCENTAGE') {
            discountAmount = validatedData.orderAmount * (coupon.discountValue.toNumber() / 100)
            if (coupon.maxDiscountAmount) {
              discountAmount = Math.min(discountAmount, coupon.maxDiscountAmount.toNumber())
            }
          }

          // 确保优惠金额不超过订单金额
          discountAmount = Math.min(discountAmount, validatedData.orderAmount)
          totalDiscount += discountAmount
        }
      }

      couponDiscounts.push({
        userCouponId: userCoupon.id,
        couponId: coupon.id,
        name: coupon.name,
        type: coupon.type,
        discountAmount,
        applicable,
        reason
      })
    }

    // 确保总优惠金额不超过订单金额
    totalDiscount = Math.min(totalDiscount, validatedData.orderAmount)

    const result = {
      orderAmount: validatedData.orderAmount,
      totalDiscount,
      finalAmount: validatedData.orderAmount - totalDiscount,
      coupons: couponDiscounts,
      summary: {
        selectedCount: validCoupons.length,
        applicableCount: couponDiscounts.filter(c => c.applicable).length,
        totalSavings: totalDiscount
      }
    }

    return createSuccessResponse(result, '优惠券计算成功')

  } catch (error) {
    console.error('计算优惠券失败:', error)
    if (error instanceof BusinessError || error instanceof ValidationError) {
      throw error
    }
    throw new InternalServerError('计算优惠券失败')
  }
})
