import jwt from 'jsonwebtoken'
import type { User } from '~/types'

/**
 * 认证工具函数
 */

// JWT payload接口
interface JWTPayload {
  userId: number
  username: string
  role: string
  iat?: number
  exp?: number
}

/**
 * 从请求头中提取token
 */
export function extractTokenFromHeader(event: any): string | null {
  const authorization = getHeader(event, 'authorization')

  if (!authorization || !authorization.startsWith('Bearer ')) {
    return null
  }

  return authorization.substring(7)
}

/**
 * 生成JWT token
 */
export function generateToken(user: Pick<User, 'id' | 'username' | 'role'>, expiresIn: string = '7d'): string {
  const config = useRuntimeConfig()

  const payload: JWTPayload = {
    userId: user.id,
    username: user.username,
    role: user.role
  }

  return jwt.sign(payload, config.jwtSecret, { expiresIn })
}

/**
 * 验证JWT token
 */
export function verifyToken(token: string): JWTPayload | null {
  try {
    const config = useRuntimeConfig()
    const decoded = jwt.verify(token, config.jwtSecret) as JWTPayload
    return decoded
  } catch (error) {
    console.error('Token验证失败:', error)
    return null
  }
}

/**
 * 刷新token
 */
export function refreshToken(oldToken: string): string | null {
  const payload = verifyToken(oldToken)
  if (!payload) {
    return null
  }

  // 生成新token
  return generateToken({
    id: payload.userId,
    username: payload.username,
    role: payload.role
  })
}

/**
 * 检查用户权限
 */
export function hasPermission(userRole: string, requiredRole: string): boolean {
  const roleHierarchy = {
    'ADMIN': 3,
    'MERCHANT': 2,
    'USER': 1
  }

  const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0
  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0

  return userLevel >= requiredLevel
}

/**
 * 验证密码强度
 */
export function validatePasswordStrength(password: string): {
  isValid: boolean
  errors: string[]
  score: number
} {
  const errors: string[] = []
  let score = 0

  // 长度检查
  if (password.length < 6) {
    errors.push('密码至少需要6个字符')
  } else if (password.length >= 8) {
    score += 1
  }

  // 包含小写字母
  if (/[a-z]/.test(password)) {
    score += 1
  } else {
    errors.push('密码应包含小写字母')
  }

  // 包含大写字母
  if (/[A-Z]/.test(password)) {
    score += 1
  }

  // 包含数字
  if (/[0-9]/.test(password)) {
    score += 1
  } else {
    errors.push('密码应包含数字')
  }

  // 包含特殊字符
  if (/[^a-zA-Z0-9]/.test(password)) {
    score += 1
  }

  // 不能是常见密码
  const commonPasswords = ['123456', 'password', '123456789', 'qwerty', 'abc123']
  if (commonPasswords.includes(password.toLowerCase())) {
    errors.push('密码过于简单，请使用更复杂的密码')
    score = 0
  }

  return {
    isValid: errors.length === 0 && score >= 2,
    errors,
    score
  }
}

/**
 * 验证用户名格式
 */
export function validateUsername(username: string): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  // 长度检查
  if (username.length < 3) {
    errors.push('用户名至少需要3个字符')
  } else if (username.length > 20) {
    errors.push('用户名最多20个字符')
  }

  // 格式检查
  if (!/^[a-zA-Z0-9_]+$/.test(username)) {
    errors.push('用户名只能包含字母、数字和下划线')
  }

  // 不能以数字开头
  if (/^[0-9]/.test(username)) {
    errors.push('用户名不能以数字开头')
  }

  // 保留用户名检查
  const reservedUsernames = ['admin', 'root', 'system', 'api', 'www', 'mail', 'support']
  if (reservedUsernames.includes(username.toLowerCase())) {
    errors.push('该用户名为系统保留，请选择其他用户名')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 生成随机字符串
 */
export function generateRandomString(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''

  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }

  return result
}

/**
 * 生成验证码
 */
export function generateCaptcha(length: number = 4): string {
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  let result = ''

  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }

  return result
}

/**
 * 验证邮箱格式
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证手机号格式
 */
export function validatePhone(phone: string): boolean {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 获取客户端IP地址
 */
export function getClientIP(event: any): string {
  const forwarded = getHeader(event, 'x-forwarded-for')
  const realIP = getHeader(event, 'x-real-ip')
  const remoteAddress = event.node.req.socket?.remoteAddress

  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }

  return realIP || remoteAddress || 'unknown'
}

/**
 * 获取用户代理
 */
export function getUserAgent(event: any): string {
  return getHeader(event, 'user-agent') || 'unknown'
}

/**
 * 记录登录日志
 */
export function logUserLogin(userId: number, ip: string, userAgent: string): void {
  // TODO: 实现登录日志记录
  console.log(`用户登录: ID=${userId}, IP=${ip}, UA=${userAgent}`)
}

/**
 * 记录登出日志
 */
export function logUserLogout(userId: number, ip: string, userAgent: string): void {
  // TODO: 实现登出日志记录
  console.log(`用户登出: ID=${userId}, IP=${ip}, UA=${userAgent}`)
}

/**
 * 检查请求频率限制
 */
export function checkRateLimit(key: string, limit: number = 10, window: number = 60): boolean {
  // TODO: 实现基于Redis的频率限制
  // 这里简化处理，实际应该使用Redis存储
  return true
}
