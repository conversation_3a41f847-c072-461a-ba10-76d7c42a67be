# PostgreSQL数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=social_shop
DATABASE_URL=postgresql://postgres:your_password@localhost:5432/social_shop

# Redis配置
REDIS_URL=redis://localhost:6379

# 日志配置
LOG_RETENTION_DAYS=90
CRON_SECRET=your-cron-secret-key-here

# JWT密钥
JWT_SECRET=your-super-secret-jwt-key-here

# API配置
NUXT_PUBLIC_API_BASE=/api

# 文件上传配置
UPLOAD_DIR=./public/uploads
MAX_FILE_SIZE=5242880

# 支付配置
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY=your_alipay_private_key
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# 短信服务配置
SMS_ACCESS_KEY=your_sms_access_key
SMS_SECRET_KEY=your_sms_secret_key

# 邮件服务配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
