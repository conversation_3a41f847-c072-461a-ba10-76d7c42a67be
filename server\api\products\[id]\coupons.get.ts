/**
 * 获取商品可用优惠券
 * GET /api/products/:id/coupons
 */

export default defineApiHandler(async (event) => {
  // 获取商品ID
  const productId = parseInt(getRouterParam(event, 'id') || '0')
  if (!productId || isNaN(productId)) {
    throw new ValidationError('商品ID格式不正确')
  }

  // 获取当前用户ID（可选）
  const userId = event.context.user?.id

  try {
    // 检查商品是否存在
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { 
        id: true, 
        name: true, 
        categoryId: true,
        merchantId: true,
        status: true 
      }
    })

    if (!product) {
      throw new NotFoundError('商品不存在')
    }

    if (product.status !== 'ACTIVE') {
      throw new BusinessError('商品已下架')
    }

    // 获取当前时间
    const now = new Date()

    // 查询可用的优惠券
    const coupons = await prisma.coupon.findMany({
      where: {
        status: 'ACTIVE',
        startTime: { lte: now },
        endTime: { gte: now },
        OR: [
          // 全场通用券
          { applicableType: 'ALL' },
          // 分类券
          { 
            applicableType: 'CATEGORY',
            applicableIds: { has: product.categoryId }
          },
          // 商品券
          { 
            applicableType: 'PRODUCT',
            applicableIds: { has: productId }
          },
          // 商家券
          { 
            applicableType: 'MERCHANT',
            applicableIds: { has: product.merchantId }
          }
        ]
      },
      orderBy: [
        { type: 'asc' }, // 同享券在前
        { discountValue: 'desc' } // 优惠金额大的在前
      ]
    })

    // 如果用户已登录，检查用户已领取的优惠券
    let userCoupons: any[] = []
    if (userId) {
      userCoupons = await prisma.userCoupon.findMany({
        where: {
          userId,
          couponId: { in: coupons.map(c => c.id) }
        },
        select: { couponId: true, status: true }
      })
    }

    // 格式化优惠券数据
    const formattedCoupons = coupons.map(coupon => {
      const userCoupon = userCoupons.find(uc => uc.couponId === coupon.id)
      
      return {
        id: coupon.id,
        name: coupon.name,
        description: coupon.description,
        type: coupon.type, // 'SHARED' | 'EXCLUSIVE'
        discountType: coupon.discountType, // 'FIXED' | 'PERCENTAGE'
        discountValue: coupon.discountValue.toNumber(),
        minOrderAmount: coupon.minOrderAmount?.toNumber() || 0,
        maxDiscountAmount: coupon.maxDiscountAmount?.toNumber() || null,
        usageLimit: coupon.usageLimit,
        usedCount: coupon.usedCount,
        startTime: coupon.startTime,
        endTime: coupon.endTime,
        applicableType: coupon.applicableType,
        isReceived: !!userCoupon,
        canUse: userCoupon?.status === 'UNUSED' || false,
        remainingCount: Math.max(0, coupon.usageLimit - coupon.usedCount)
      }
    })

    // 按类型分组
    const sharedCoupons = formattedCoupons.filter(c => c.type === 'SHARED')
    const exclusiveCoupons = formattedCoupons.filter(c => c.type === 'EXCLUSIVE')

    const result = {
      productId,
      productName: product.name,
      coupons: {
        shared: sharedCoupons,
        exclusive: exclusiveCoupons,
        total: formattedCoupons.length
      }
    }

    return createSuccessResponse(result, '获取优惠券列表成功')

  } catch (error) {
    console.error('获取商品优惠券失败:', error)
    throw new InternalServerError('获取优惠券失败')
  }
})
