import { z } from 'zod'

/**
 * 添加商品到购物车
 * POST /api/cart
 */

// 请求数据验证schema
const addToCartSchema = z.object({
  productId: z.number().int().positive('商品ID必须是正整数'),
  quantity: z.number().int().min(1, '数量至少为1').max(999, '数量不能超过999').default(1)
})

export default defineApiHandler(async event => {
  // 只允许POST请求
  assertMethod(event, 'POST')

  // 获取当前用户ID
  const userId = event.context.user?.id
  if (!userId) {
    throw new AuthenticationError('请先登录')
  }

  // 获取请求体数据
  const body = await readBody(event)

  // 验证请求数据
  const { productId, quantity } = addToCartSchema.parse(body)

  // 检查商品是否存在且可购买
  const product = await prisma.product.findUnique({
    where: { id: productId },
    select: {
      id: true,
      name: true,
      price: true,
      stock: true,
      status: true,
      merchantId: true
    }
  })

  if (!product) {
    throw new NotFoundError('商品不存在')
  }

  if (product.status !== 'ACTIVE') {
    throw new BusinessError('商品已下架')
  }

  if (product.stock < quantity) {
    throw new BusinessError(`库存不足，当前库存：${product.stock}`)
  }

  // 检查是否已在购物车中
  const existingCartItem = await prisma.cartItem.findUnique({
    where: {
      userId_productId: {
        userId,
        productId
      }
    }
  })

  let cartItem
  if (existingCartItem) {
    // 更新数量
    const newQuantity = existingCartItem.quantity + quantity

    if (newQuantity > product.stock) {
      throw new BusinessError(`库存不足，当前库存：${product.stock}，购物车已有：${existingCartItem.quantity}`)
    }

    cartItem = await prisma.cartItem.update({
      where: { id: existingCartItem.id },
      data: {
        quantity: newQuantity,
        updatedAt: new Date()
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            price: true,
            originalPrice: true,
            images: true,
            stock: true
          }
        }
      }
    })
  } else {
    // 创建新的购物车项目
    cartItem = await prisma.cartItem.create({
      data: {
        userId,
        productId,
        quantity,
        selected: true
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            price: true,
            originalPrice: true,
            images: true,
            stock: true
          }
        }
      }
    })
  }

  // 格式化返回数据
  const formattedCartItem = {
    ...cartItem,
    product: {
      ...cartItem.product,
      price: cartItem.product.price.toNumber(),
      originalPrice: cartItem.product.originalPrice?.toNumber() || null
    }
  }

  // 记录购物车操作日志
  await logCartAction('CART_ADD', productId, event, userId, {
    quantity: existingCartItem ? quantity : cartItem.quantity,
    isUpdate: !!existingCartItem,
    productName: product.name,
    productPrice: product.price.toNumber()
  })

  return createSuccessResponse(formattedCartItem, existingCartItem ? '购物车商品数量已更新' : '商品已添加到购物车')
})
