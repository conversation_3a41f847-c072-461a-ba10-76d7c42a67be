/**
 * 获取商品列表
 * GET /api/products
 */

export default defineApiHandler(async (event) => {
  const query = getQuery(event)
  
  // 解析分页参数
  const { page, pageSize, skip, take } = parsePaginationQuery(query)
  
  // 解析排序参数
  const allowedSortFields = ['price', 'sales', 'rating', 'createdAt']
  const orderBy = parseSortQuery(query, allowedSortFields)
  
  // 解析搜索和过滤参数
  const { keyword, filters } = parseSearchQuery(query)
  
  // 构建查询条件
  const where: any = {
    status: 'ACTIVE'
  }
  
  // 关键词搜索
  if (keyword) {
    where.OR = [
      { name: { contains: keyword, mode: 'insensitive' } },
      { description: { contains: keyword, mode: 'insensitive' } },
      { tags: { has: keyword } }
    ]
  }
  
  // 分类过滤
  if (filters.categoryId) {
    where.categoryId = parseInt(filters.categoryId)
  }
  
  // 价格范围过滤
  if (filters.minPrice || filters.maxPrice) {
    where.price = {}
    if (filters.minPrice) {
      where.price.gte = parseFloat(filters.minPrice)
    }
    if (filters.maxPrice) {
      where.price.lte = parseFloat(filters.maxPrice)
    }
  }
  
  // 商家过滤
  if (filters.merchantId) {
    where.merchantId = parseInt(filters.merchantId)
  }
  
  // 库存过滤
  if (filters.inStock === 'true') {
    where.stock = { gt: 0 }
  }
  
  // 查询商品列表和总数
  const [products, total] = await Promise.all([
    prisma.product.findMany({
      where,
      orderBy,
      skip,
      take,
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        },
        merchant: {
          select: {
            id: true,
            username: true,
            nickname: true,
            avatar: true
          }
        },
        _count: {
          select: {
            reviews: true,
            favorites: true
          }
        }
      }
    }),
    prisma.product.count({ where })
  ])
  
  // 格式化商品数据
  const formattedProducts = products.map(product => ({
    ...product,
    reviewCount: product._count.reviews,
    favoriteCount: product._count.favorites,
    _count: undefined
  }))
  
  return createPaginatedResponse(
    formattedProducts,
    total,
    page,
    pageSize,
    '获取商品列表成功'
  )
})
