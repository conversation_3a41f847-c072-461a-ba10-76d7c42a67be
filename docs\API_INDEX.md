# 📚 API文档索引

本文档提供了项目中所有API接口的快速索引和导航。

## 📋 文档结构

| 文档 | 说明 | 适用对象 |
|------|------|----------|
| [API.md](./API.md) | 完整的API接口文档 | 所有开发者 |
| [API_LOGS.md](./API_LOGS.md) | 日志管理API详细文档 | 管理员、运维人员 |

## 🔍 快速导航

### 🔐 认证相关

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 用户注册 | POST | `/api/auth/register` | 新用户注册 |
| 用户登录 | POST | `/api/auth/login` | 用户登录获取token |
| 用户登出 | POST | `/api/auth/logout` | 用户登出 |
| 刷新token | POST | `/api/auth/refresh` | 刷新访问token |
| 检查用户名 | GET | `/api/auth/check-username` | 检查用户名是否可用 |
| 检查邮箱 | GET | `/api/auth/check-email` | 检查邮箱是否可用 |

### 👤 用户管理

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 获取个人信息 | GET | `/api/users/profile` | 获取当前用户信息 |
| 更新个人信息 | PUT | `/api/users/profile` | 更新用户资料 |
| 获取用户信息 | GET | `/api/users/:id` | 获取指定用户信息 |
| 用户订单列表 | GET | `/api/users/orders` | 获取用户订单 |
| 用户收藏列表 | GET | `/api/users/favorites` | 获取用户收藏 |

### 🛍️ 商品相关

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 商品列表 | GET | `/api/products` | 获取商品列表 |
| 商品详情 | GET | `/api/products/:id` | 获取商品详细信息 |
| 商品搜索 | GET | `/api/products/search` | 搜索商品 |
| 搜索建议 | GET | `/api/search/suggestions` | 获取搜索建议 |
| 商品分类 | GET | `/api/categories` | 获取商品分类 |

### 🛒 购物车和订单

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 购物车列表 | GET | `/api/cart` | 获取购物车商品 |
| 添加到购物车 | POST | `/api/cart` | 添加商品到购物车 |
| 创建订单 | POST | `/api/orders` | 创建新订单 |
| 订单列表 | GET | `/api/orders` | 获取订单列表 |
| 订单详情 | GET | `/api/orders/:id` | 获取订单详情 |

### 🎫 优惠券

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 优惠券列表 | GET | `/api/coupons` | 获取可用优惠券 |
| 领取优惠券 | POST | `/api/coupons/:id/claim` | 领取优惠券 |
| 我的优惠券 | GET | `/api/users/coupons` | 获取用户优惠券 |

### 📱 社交功能

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 动态列表 | GET | `/api/posts` | 获取动态列表 |
| 发布动态 | POST | `/api/posts` | 发布新动态 |
| 点赞动态 | POST | `/api/posts/:id/like` | 点赞/取消点赞 |
| 评论列表 | GET | `/api/comments` | 获取评论列表 |
| 发布评论 | POST | `/api/comments` | 发布评论 |
| 关注用户 | POST | `/api/users/:id/follow` | 关注用户 |

### 📊 日志管理 (管理员)

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 搜索日志 | GET | `/api/admin/logs/search` | 获取搜索日志列表 |
| 用户操作日志 | GET | `/api/admin/logs/users` | 获取用户操作日志 |
| 日志统计 | GET | `/api/admin/logs/stats` | 获取日志统计信息 |
| 清理日志 | POST | `/api/admin/logs/cleanup` | 清理过期日志 |
| 定时清理 | POST | `/api/cron/cleanup-logs` | 定时任务清理日志 |

### 📂 文件上传

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 上传图片 | POST | `/api/upload/image` | 上传图片文件 |
| 上传头像 | POST | `/api/upload/avatar` | 上传用户头像 |

### 🔍 系统接口

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 健康检查 | GET | `/api/health` | 系统健康状态检查 |

## 🔐 权限说明

### 公开接口 (无需认证)
- 商品列表和详情
- 商品搜索
- 用户注册
- 健康检查

### 用户接口 (需要登录)
- 个人信息管理
- 购物车操作
- 订单管理
- 社交功能
- 优惠券操作

### 管理员接口 (需要管理员权限)
- 日志管理相关接口
- 系统管理功能

### 定时任务接口 (需要特殊密钥)
- 定时清理日志

## 📊 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 响应数据
  }
}
```

### 分页响应
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "items": [],
    "total": 100,
    "page": 1,
    "pageSize": 20,
    "totalPages": 5
  }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误信息",
  "errors": ["详细错误信息"]
}
```

## 🔧 常用状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 422 | 业务逻辑错误 |
| 500 | 服务器内部错误 |

## 🚀 开发建议

1. **认证**: 在需要认证的接口中包含JWT token
2. **错误处理**: 始终检查响应的`success`字段
3. **分页**: 使用分页参数避免一次性加载大量数据
4. **缓存**: 对不经常变化的数据进行适当缓存
5. **重试**: 对网络错误实现重试机制
6. **日志**: 重要操作会自动记录日志，便于追踪和分析

## 📖 相关文档

- [完整API文档](./API.md) - 详细的接口说明和示例
- [日志API文档](./API_LOGS.md) - 日志管理接口详细说明
- [快速开始](./QUICK_START.md) - 项目快速搭建指南
- [配置指南](./CONFIGURATION.md) - 系统配置说明
- [日志系统](./LOGGING.md) - 日志系统使用指南

## 🔗 在线工具

建议使用以下工具进行API测试：

- **Postman** - API测试工具
- **Insomnia** - REST客户端
- **curl** - 命令行工具
- **HTTPie** - 现代命令行HTTP客户端

## 📞 技术支持

如果在使用API过程中遇到问题：

1. 首先查看相关文档
2. 检查请求格式和参数
3. 查看错误响应信息
4. 提交Issue或联系技术支持
