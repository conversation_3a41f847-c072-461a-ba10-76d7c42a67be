/**
 * 关注/取消关注用户
 * POST /api/users/:id/follow
 */

export default defineApiHandler(async (event) => {
  // 只允许POST请求
  assertMethod(event, 'POST')

  // 获取当前用户ID
  const currentUserId = event.context.user?.id
  if (!currentUserId) {
    throw new AuthenticationError('请先登录')
  }

  // 获取目标用户ID
  const targetUserId = parseInt(getRouterParam(event, 'id') || '0')
  if (!targetUserId || isNaN(targetUserId)) {
    throw new ValidationError('用户ID格式不正确')
  }

  // 不能关注自己
  if (currentUserId === targetUserId) {
    throw new BusinessError('不能关注自己')
  }

  // 检查目标用户是否存在
  const targetUser = await prisma.user.findUnique({
    where: { id: targetUserId },
    select: {
      id: true,
      username: true,
      nickname: true,
      status: true
    }
  })

  if (!targetUser || targetUser.status !== 'ACTIVE') {
    throw new NotFoundError('用户不存在')
  }

  // 检查是否已关注
  const existingFollow = await prisma.follow.findUnique({
    where: {
      followerId_followingId: {
        followerId: currentUserId,
        followingId: targetUserId
      }
    }
  })

  let isFollowing: boolean
  let message: string

  if (existingFollow) {
    // 取消关注
    await prisma.follow.delete({
      where: { id: existingFollow.id }
    })
    isFollowing = false
    message = '已取消关注'
  } else {
    // 添加关注
    await prisma.follow.create({
      data: {
        followerId: currentUserId,
        followingId: targetUserId
      }
    })
    isFollowing = true
    message = '关注成功'

    // TODO: 发送关注通知
    // await sendFollowNotification(targetUserId, currentUserId)
  }

  // 获取最新的关注统计
  const [followersCount, followingCount] = await Promise.all([
    prisma.follow.count({
      where: { followingId: targetUserId }
    }),
    prisma.follow.count({
      where: { followerId: targetUserId }
    })
  ])

  return createSuccessResponse({
    isFollowing,
    targetUser: {
      ...targetUser,
      followersCount,
      followingCount
    }
  }, message)
})
