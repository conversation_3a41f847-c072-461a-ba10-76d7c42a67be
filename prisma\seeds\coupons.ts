import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function seedCoupons() {
  console.log('开始创建优惠券数据...')

  // 创建优惠券数据
  const coupons = [
    {
      name: '新用户专享券',
      description: '新用户注册专享，满100元减20元',
      type: 'SHARED',
      discountType: 'FIXED',
      discountValue: 20,
      minOrderAmount: 100,
      totalCount: 10000,
      perUserLimit: 1,
      applicableScope: 'ALL',
      applicableIds: [],
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-12-31'),
      status: 'ACTIVE'
    },
    {
      name: '全场8折券',
      description: '全场商品8折优惠，最高优惠50元',
      type: 'EXCLUSIVE',
      discountType: 'PERCENTAGE',
      discountValue: 20, // 20% off = 8折
      maxDiscountAmount: 50,
      minOrderAmount: 200,
      totalCount: 5000,
      perUserLimit: 2,
      applicableScope: 'ALL',
      applicableIds: [],
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-12-31'),
      status: 'ACTIVE'
    },
    {
      name: '满300减50',
      description: '全场满300元减50元',
      type: 'SHARED',
      discountType: 'FIXED',
      discountValue: 50,
      minOrderAmount: 300,
      totalCount: 8000,
      perUserLimit: 3,
      applicableScope: 'ALL',
      applicableIds: [],
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-12-31'),
      status: 'ACTIVE'
    },
    {
      name: '电子产品专享券',
      description: '电子产品分类专享，满500减80元',
      type: 'SHARED',
      discountType: 'FIXED',
      discountValue: 80,
      minOrderAmount: 500,
      totalCount: 3000,
      perUserLimit: 2,
      applicableScope: 'CATEGORY',
      applicableIds: [1], // 假设分类ID为1是电子产品
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-12-31'),
      status: 'ACTIVE'
    },
    {
      name: '限时9折券',
      description: '限时特惠，全场9折优惠券',
      type: 'EXCLUSIVE',
      discountType: 'PERCENTAGE',
      discountValue: 10, // 10% off = 9折
      maxDiscountAmount: 100,
      minOrderAmount: 100,
      totalCount: 2000,
      perUserLimit: 1,
      applicableScope: 'ALL',
      applicableIds: [],
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-06-30'),
      status: 'ACTIVE'
    },
    {
      name: '服装类满200减30',
      description: '服装类商品满200减30元',
      type: 'SHARED',
      discountType: 'FIXED',
      discountValue: 30,
      minOrderAmount: 200,
      totalCount: 5000,
      perUserLimit: 5,
      applicableScope: 'CATEGORY',
      applicableIds: [2], // 假设分类ID为2是服装
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-12-31'),
      status: 'ACTIVE'
    },
    {
      name: '超级会员专享',
      description: '超级会员专享，满1000减200元',
      type: 'EXCLUSIVE',
      discountType: 'FIXED',
      discountValue: 200,
      minOrderAmount: 1000,
      totalCount: 1000,
      perUserLimit: 1,
      applicableScope: 'ALL',
      applicableIds: [],
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-12-31'),
      status: 'ACTIVE'
    },
    {
      name: '周末特惠券',
      description: '周末专享，全场95折',
      type: 'SHARED',
      discountType: 'PERCENTAGE',
      discountValue: 5, // 5% off = 95折
      maxDiscountAmount: 25,
      minOrderAmount: 50,
      totalCount: 10000,
      perUserLimit: 10,
      applicableScope: 'ALL',
      applicableIds: [],
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-12-31'),
      status: 'ACTIVE'
    }
  ]

  // 批量创建优惠券
  for (const couponData of coupons) {
    await prisma.coupon.create({
      data: couponData
    })
  }

  console.log(`✅ 成功创建 ${coupons.length} 个优惠券`)
}

// 如果直接运行此文件
if (require.main === module) {
  seedCoupons()
    .catch((e) => {
      console.error('创建优惠券数据失败:', e)
      process.exit(1)
    })
    .finally(async () => {
      await prisma.$disconnect()
    })
}
