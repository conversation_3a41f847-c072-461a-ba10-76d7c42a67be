import type { Post, User, PaginatedResponse } from '~/types'

/**
 * 社交功能状态管理
 */
export const useSocialStore = defineStore('social', () => {
  // 状态
  const posts = ref<Post[]>([])
  const currentPost = ref<Post | null>(null)
  const following = ref<Set<number>>(new Set())
  const followers = ref<User[]>([])
  const followingUsers = ref<User[]>([])
  const isLoading = ref(false)
  const pagination = ref({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  })

  // API实例
  const request = createAuthenticatedRequest()
  const authStore = useAuthStore()
  const toast = useToast()

  // 获取动态列表
  const fetchPosts = async (params: Record<string, any> = {}) => {
    try {
      isLoading.value = true
      
      const response = await request.get<PaginatedResponse<Post>>('/api/posts', {
        params: {
          page: pagination.value.page,
          pageSize: pagination.value.pageSize,
          ...params
        },
        requireAuth: false
      })
      
      posts.value = response.items
      pagination.value = {
        page: response.page,
        pageSize: response.pageSize,
        total: response.total,
        totalPages: response.totalPages
      }
    } catch (error: any) {
      console.error('获取动态列表失败:', error)
      toast.add({
        title: '获取动态失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
    } finally {
      isLoading.value = false
    }
  }

  // 发布动态
  const createPost = async (postData: {
    content: string
    images?: string[]
    type?: 'TEXT' | 'IMAGE' | 'PRODUCT_SHARE'
    productId?: number
  }) => {
    if (!authStore.isLoggedIn) {
      toast.add({
        title: '请先登录',
        description: '登录后即可发布动态',
        color: 'yellow'
      })
      return navigateTo('/login')
    }

    try {
      const post = await request.post<Post>('/api/posts', postData)
      
      // 将新动态添加到列表开头
      posts.value.unshift(post)
      
      toast.add({
        title: '发布成功',
        description: '动态已发布',
        color: 'green'
      })
      
      return post
    } catch (error: any) {
      console.error('发布动态失败:', error)
      toast.add({
        title: '发布失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 点赞/取消点赞动态
  const toggleLike = async (postId: number) => {
    if (!authStore.isLoggedIn) {
      toast.add({
        title: '请先登录',
        description: '登录后即可点赞',
        color: 'yellow'
      })
      return navigateTo('/login')
    }

    try {
      const response = await request.post<{ isLiked: boolean, likesCount: number }>(`/api/posts/${postId}/like`)
      
      // 更新本地状态
      const postIndex = posts.value.findIndex(post => post.id === postId)
      if (postIndex !== -1) {
        posts.value[postIndex].isLiked = response.isLiked
        posts.value[postIndex].likesCount = response.likesCount
      }
      
      if (currentPost.value?.id === postId) {
        currentPost.value.isLiked = response.isLiked
        currentPost.value.likesCount = response.likesCount
      }
    } catch (error: any) {
      console.error('点赞操作失败:', error)
      toast.add({
        title: '操作失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
    }
  }

  // 评论动态
  const commentPost = async (postId: number, content: string) => {
    if (!authStore.isLoggedIn) {
      toast.add({
        title: '请先登录',
        description: '登录后即可评论',
        color: 'yellow'
      })
      return navigateTo('/login')
    }

    try {
      const comment = await request.post(`/api/posts/${postId}/comments`, { content })
      
      // 更新评论数量
      const postIndex = posts.value.findIndex(post => post.id === postId)
      if (postIndex !== -1) {
        posts.value[postIndex].commentsCount += 1
      }
      
      toast.add({
        title: '评论成功',
        color: 'green'
      })
      
      return comment
    } catch (error: any) {
      console.error('评论失败:', error)
      toast.add({
        title: '评论失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 关注用户
  const followUser = async (userId: number) => {
    if (!authStore.isLoggedIn) {
      toast.add({
        title: '请先登录',
        description: '登录后即可关注用户',
        color: 'yellow'
      })
      return navigateTo('/login')
    }

    try {
      const response = await request.post<{
        isFollowing: boolean
        targetUser: User & { followersCount: number, followingCount: number }
      }>(`/api/users/${userId}/follow`)
      
      if (response.isFollowing) {
        following.value.add(userId)
        toast.add({
          title: '关注成功',
          color: 'green'
        })
      } else {
        following.value.delete(userId)
        toast.add({
          title: '已取消关注',
          color: 'blue'
        })
      }
      
      return response
    } catch (error: any) {
      console.error('关注操作失败:', error)
      toast.add({
        title: '操作失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 获取关注列表
  const fetchFollowing = async (userId?: number) => {
    try {
      const targetUserId = userId || authStore.user?.id
      if (!targetUserId) return
      
      const users = await request.get<User[]>(`/api/users/${targetUserId}/following`)
      followingUsers.value = users
      
      // 更新关注状态
      following.value.clear()
      users.forEach(user => following.value.add(user.id))
      
      return users
    } catch (error: any) {
      console.error('获取关注列表失败:', error)
      throw error
    }
  }

  // 获取粉丝列表
  const fetchFollowers = async (userId?: number) => {
    try {
      const targetUserId = userId || authStore.user?.id
      if (!targetUserId) return
      
      const users = await request.get<User[]>(`/api/users/${targetUserId}/followers`)
      followers.value = users
      
      return users
    } catch (error: any) {
      console.error('获取粉丝列表失败:', error)
      throw error
    }
  }

  // 删除动态
  const deletePost = async (postId: number) => {
    if (!authStore.isLoggedIn) return

    try {
      await request.delete(`/api/posts/${postId}`)
      
      // 从本地列表中移除
      const index = posts.value.findIndex(post => post.id === postId)
      if (index !== -1) {
        posts.value.splice(index, 1)
      }
      
      toast.add({
        title: '删除成功',
        color: 'blue'
      })
    } catch (error: any) {
      console.error('删除动态失败:', error)
      toast.add({
        title: '删除失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 举报动态
  const reportPost = async (postId: number, reason: string) => {
    if (!authStore.isLoggedIn) {
      return navigateTo('/login')
    }

    try {
      await request.post(`/api/posts/${postId}/report`, { reason })
      
      toast.add({
        title: '举报成功',
        description: '我们会尽快处理您的举报',
        color: 'blue'
      })
    } catch (error: any) {
      console.error('举报失败:', error)
      toast.add({
        title: '举报失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 计算属性
  const isFollowing = (userId: number) => following.value.has(userId)

  // 清空状态
  const clearState = () => {
    posts.value = []
    currentPost.value = null
    following.value.clear()
    followers.value = []
    followingUsers.value = []
    pagination.value = {
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    }
  }

  return {
    // 状态
    posts: readonly(posts),
    currentPost: readonly(currentPost),
    following: readonly(following),
    followers: readonly(followers),
    followingUsers: readonly(followingUsers),
    isLoading: readonly(isLoading),
    pagination: readonly(pagination),
    
    // 方法
    fetchPosts,
    createPost,
    toggleLike,
    commentPost,
    followUser,
    fetchFollowing,
    fetchFollowers,
    deletePost,
    reportPost,
    
    // 计算属性
    isFollowing,
    
    // 工具方法
    clearState
  }
})
