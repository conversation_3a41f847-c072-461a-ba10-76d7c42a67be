/**
 * 全局认证中间件
 * 检查用户登录状态，保护需要认证的路由
 */

export default defineNuxtRouteMiddleware(async (to) => {
  // 不需要认证的路由
  const publicRoutes = [
    '/',
    '/login',
    '/register',
    '/products',
    '/help',
    '/about',
    '/contact',
    '/terms',
    '/privacy',
    '/forgot-password'
  ]

  // 检查是否为公开路由或以公开路径开头
  const isPublicRoute = publicRoutes.some(route => {
    if (route === '/') {
      return to.path === '/'
    }
    return to.path.startsWith(route)
  })

  // 如果是公开路由，直接通过
  if (isPublicRoute) {
    return
  }

  // 检查是否为API路由（服务端处理）
  if (to.path.startsWith('/api/')) {
    return
  }

  // 获取认证store
  const authStore = useAuthStore()

  // 在客户端检查认证状态
  if (process.client) {
    // 如果没有登录，重定向到登录页
    if (!authStore.isLoggedIn) {
      return navigateTo(`/login?redirect=${encodeURIComponent(to.fullPath)}`)
    }

    // 验证token有效性
    const isValid = await authStore.validateToken()
    if (!isValid) {
      return navigateTo(`/login?redirect=${encodeURIComponent(to.fullPath)}`)
    }
  }
})
