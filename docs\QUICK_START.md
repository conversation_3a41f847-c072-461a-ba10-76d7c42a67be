# 🚀 快速开始指南

本指南将帮助您快速搭建和运行社交电商项目。

## 📋 前置要求

在开始之前，请确保您的系统已安装以下软件：

- **Node.js** (版本 18.0 或更高) - [下载地址](https://nodejs.org/)
- **PostgreSQL** (版本 12 或更高) - [下载地址](https://www.postgresql.org/download/)
- **Git** - [下载地址](https://git-scm.com/)

## 🛠️ 一键初始化

### 方法一：使用初始化脚本（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd shop

# 交互式初始化（推荐）
npm run setup        # Linux/macOS
npm run setup:win    # Windows

# 跳过配置询问
npm run setup:skip        # Linux/macOS
npm run setup:skip:win    # Windows

# 静默模式（自动化部署）
npm run setup:silent        # Linux/macOS
npm run setup:silent:win    # Windows
```

**初始化模式说明：**

| 模式         | 命令                   | 说明                      | 适用场景                 |
| ------------ | ---------------------- | ------------------------- | ------------------------ |
| **交互式**   | `npm run setup`        | 询问数据库和Redis配置信息 | 首次安装，需要自定义配置 |
| **跳过配置** | `npm run setup:skip`   | 使用.env.example默认配置  | 快速搭建，后续手动配置   |
| **静默模式** | `npm run setup:silent` | 自动使用默认配置，无交互  | 自动化部署，CI/CD环境    |

**初始化脚本会自动完成：**

- ✅ 检查必要工具
- ✅ 安装项目依赖
- ✅ 创建上传目录
- ✅ 复制环境变量配置
- ✅ 配置数据库和Redis连接（交互式/静默模式）
- ✅ 初始化数据库
- ✅ 填充测试数据

**交互式配置示例：**

```
🔧 数据库和Redis配置

是否需要配置数据库连接？(y/N): y
请输入数据库连接信息（按回车使用默认值）：
数据库主机 [localhost]:
数据库端口 [5432]:
数据库用户名 [postgres]:
数据库密码: your_password
数据库名称 [shop_db]:
✅ 数据库配置已更新

🔗 测试数据库连接...
❌ 数据库连接失败
🔍 诊断信息：
  数据库主机: localhost
  数据库端口: 5432
  数据库用户: postgres
  数据库名称: shop_db
  ❌ 主机端口不可达

可能的原因：
1. 数据库服务未启动
2. 数据库连接信息错误
3. 数据库用户权限不足
4. 网络连接问题

是否重新配置数据库连接？(y/N): y
```

**智能重试机制：**

- 🔄 最多重试3次数据库连接
- 🔍 提供详细的连接诊断信息
- 🛠️ 连接失败时询问是否重新配置
- 📋 显示可能的失败原因和解决建议

### 方法二：手动初始化

如果自动初始化失败，可以按以下步骤手动操作：

```bash
# 1. 安装依赖
npm install

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库连接

# 3. 创建上传目录
npm run uploads:init        # Linux/macOS
npm run uploads:init:win    # Windows

# 4. 初始化数据库
npm run db:init        # Linux/macOS
npm run db:init:win    # Windows
```

## ⚙️ 环境配置

编辑 `.env` 文件，配置以下必要参数：

```env
# 数据库配置（必须）
DATABASE_URL="postgresql://postgres:your_password@localhost:5432/shop_db"

# JWT密钥（必须）
JWT_SECRET="your-super-secret-jwt-key-here"

# 其他配置（可选）
PORT=3000
BASE_URL="http://localhost:3000"
```

## 🗄️ 数据库设置

### 创建数据库

```sql
-- 连接到 PostgreSQL
psql -U postgres

-- 创建数据库
CREATE DATABASE shop_db;

-- 创建用户（可选）
CREATE USER shop_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE shop_db TO shop_user;
```

### 初始化数据库架构

```bash
# 生成 Prisma Client
npm run db:generate

# 推送数据库架构
npx prisma db push

# 填充测试数据
npm run db:seed
```

## 🚀 启动应用

```bash
# 启动开发服务器
npm run dev

# 应用将在以下地址运行：
# http://localhost:3000
```

## 👤 测试账号

初始化完成后，您可以使用以下测试账号登录：

| 角色   | 邮箱                 | 密码   | 说明           |
| ------ | -------------------- | ------ | -------------- |
| 管理员 | <EMAIL>    | 123456 | 系统管理员权限 |
| 商家   | <EMAIL> | 123456 | 商家管理权限   |
| 用户   | <EMAIL>     | 123456 | 普通用户权限   |

## 🛠️ 常用命令

```bash
# 开发相关
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run preview      # 预览生产版本

# 数据库相关
npm run db:studio    # 打开数据库管理界面
npm run db:seed      # 重新填充测试数据
npm run db:reset     # 重置数据库

# 代码质量
npm run lint         # 代码检查
npm run type-check   # 类型检查
```

## 📁 项目结构

```
shop/
├── components/          # Vue 组件
├── pages/              # 页面文件
├── server/             # 服务端 API
├── stores/             # Pinia 状态管理
├── types/              # TypeScript 类型定义
├── prisma/             # 数据库架构和迁移
├── public/             # 静态资源
│   └── uploads/        # 文件上传目录
├── docs/               # 项目文档
└── scripts/            # 初始化脚本
```

## 🌟 功能特性

- ✅ **用户系统**: 注册、登录、个人信息管理
- ✅ **商品管理**: 分类、商品展示、库存管理
- ✅ **购物功能**: 购物车、订单、支付流程
- ✅ **优惠券系统**: 同享券、互斥券、优惠计算
- ✅ **社交功能**: 动态发布、关注、评论、点赞
- ✅ **文件上传**: 图片上传、头像更换
- ✅ **收藏评价**: 商品收藏、评价系统

## 🔧 故障排除

### 常见问题

**1. 数据库连接失败**

- 检查 PostgreSQL 服务是否启动
- 验证 `.env` 文件中的数据库配置
- 确认数据库和用户是否存在

**2. 端口被占用**

- 修改 `.env` 文件中的 `PORT` 配置
- 或者停止占用端口的其他服务

**3. 依赖安装失败**

- 清除 npm 缓存：`npm cache clean --force`
- 删除 `node_modules` 重新安装：`rm -rf node_modules && npm install`

**4. Prisma 相关错误**

- 重新生成客户端：`npm run db:generate`
- 重置数据库：`npm run db:reset`

### 获取帮助

如果遇到问题，可以：

1. 查看项目文档：`docs/` 目录
2. 检查 API 文档：`docs/API.md`
3. 查看错误日志：控制台输出
4. 提交 Issue 或联系开发团队

## 🎉 开始使用

现在您可以：

1. 访问 http://localhost:3000 查看应用
2. 使用测试账号登录体验功能
3. 查看 `docs/API.md` 了解 API 接口
4. 开始您的开发之旅！

祝您使用愉快！ 🚀
