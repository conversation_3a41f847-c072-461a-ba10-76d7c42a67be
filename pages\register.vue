<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- 头部 -->
      <div class="text-center">
        <NuxtLink to="/" class="flex items-center justify-center space-x-2 mb-6">
          <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-lg">S</span>
          </div>
          <span class="text-2xl font-bold text-gray-900">社交购物</span>
        </NuxtLink>
        <h2 class="text-3xl font-bold text-gray-900">创建新账户</h2>
        <p class="mt-2 text-sm text-gray-600">
          已有账户？
          <NuxtLink to="/login" class="font-medium text-primary-600 hover:text-primary-500 transition-colors">
            立即登录
          </NuxtLink>
        </p>
      </div>

      <!-- 注册表单 -->
      <UCard class="p-6">
        <UForm ref="registerForm" :schema="registerSchema" :state="formData" @submit="handleRegister" class="space-y-6">
          <!-- 用户名 -->
          <UFormGroup label="用户名" name="username" required :help="usernameHelp">
            <div class="relative">
              <UInput
                v-model="formData.username"
                placeholder="请输入用户名"
                size="lg"
                :disabled="authStore.isLoading"
                autocomplete="username"
                @input="validateUsername"
                :class="{
                  'border-green-500': usernameStatus.available === true,
                  'border-red-500': usernameStatus.available === false
                }"
              />

              <!-- 状态图标 -->
              <div class="absolute right-3 top-1/2 -translate-y-1/2">
                <!-- 检查中 -->
                <div
                  v-if="usernameStatus.checking"
                  class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"
                ></div>
                <!-- 可用 -->
                <Icon
                  v-else-if="usernameStatus.available === true"
                  name="heroicons:check-circle"
                  class="w-5 h-5 text-green-500"
                />
                <!-- 不可用 -->
                <Icon
                  v-else-if="usernameStatus.available === false"
                  name="heroicons:x-circle"
                  class="w-5 h-5 text-red-500"
                />
              </div>
            </div>

            <!-- 状态消息 -->
            <div
              v-if="usernameStatus.message"
              class="mt-1 text-sm"
              :class="{
                'text-green-600': usernameStatus.available === true,
                'text-red-600': usernameStatus.available === false,
                'text-gray-600': usernameStatus.checking
              }"
            >
              {{ usernameStatus.message }}
            </div>
          </UFormGroup>

          <!-- 邮箱 -->
          <UFormGroup label="邮箱地址" name="email" required>
            <div class="relative">
              <UInput
                v-model="formData.email"
                type="email"
                placeholder="请输入邮箱地址"
                size="lg"
                :disabled="authStore.isLoading"
                autocomplete="email"
                @input="validateEmail"
                :class="{
                  'border-green-500': emailStatus.valid === true && emailStatus.available === true,
                  'border-red-500':
                    (emailStatus.valid === false || emailStatus.available === false) && formData.email.trim().length > 0
                }"
              />

              <!-- 状态图标 -->
              <div class="absolute right-3 top-1/2 -translate-y-1/2">
                <!-- 检查中 -->
                <div
                  v-if="emailStatus.checking"
                  class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"
                ></div>
                <!-- 有效且可用 -->
                <Icon
                  v-else-if="emailStatus.valid === true && emailStatus.available === true"
                  name="heroicons:check-circle"
                  class="w-5 h-5 text-green-500"
                />
                <!-- 无效或不可用 -->
                <Icon
                  v-else-if="
                    (emailStatus.valid === false || emailStatus.available === false) && formData.email.trim().length > 0
                  "
                  name="heroicons:x-circle"
                  class="w-5 h-5 text-red-500"
                />
              </div>
            </div>

            <!-- 状态消息 -->
            <div
              v-if="emailStatus.message"
              class="mt-1 text-sm"
              :class="{
                'text-green-600': emailStatus.valid === true && emailStatus.available === true,
                'text-red-600': emailStatus.valid === false || emailStatus.available === false,
                'text-gray-600': emailStatus.checking
              }"
            >
              {{ emailStatus.message }}
            </div>
          </UFormGroup>

          <!-- 手机号 -->
          <UFormGroup label="手机号" name="phone" :help="'可选，用于找回密码和接收通知'">
            <UInput
              v-model="formData.phone"
              placeholder="请输入手机号"
              size="lg"
              :disabled="authStore.isLoading"
              autocomplete="tel"
            />
          </UFormGroup>

          <!-- 密码 -->
          <UFormGroup label="密码" name="password" required :help="passwordHelp">
            <UInput
              v-model="formData.password"
              type="password"
              placeholder="请输入密码"
              size="lg"
              :disabled="authStore.isLoading"
              autocomplete="new-password"
              @input="checkPasswordStrength"
            />

            <!-- 密码强度指示器 -->
            <div v-if="formData.password" class="mt-2">
              <div class="flex items-center space-x-2">
                <div class="flex-1 bg-gray-200 rounded-full h-2">
                  <div
                    class="h-2 rounded-full transition-all duration-300"
                    :class="passwordStrengthColor"
                    :style="{ width: passwordStrengthWidth }"
                  />
                </div>
                <span class="text-xs font-medium" :class="passwordStrengthColor.replace('bg-', 'text-')">
                  {{ passwordStrengthText }}
                </span>
              </div>
            </div>
          </UFormGroup>

          <!-- 确认密码 -->
          <UFormGroup label="确认密码" name="confirmPassword" required>
            <UInput
              v-model="formData.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
              size="lg"
              :disabled="authStore.isLoading"
              autocomplete="new-password"
            />
          </UFormGroup>

          <!-- 验证码 -->
          <UFormGroup label="验证码" name="captcha" required>
            <div class="flex space-x-3">
              <UInput
                v-model="formData.captcha"
                placeholder="请输入验证码"
                size="lg"
                :disabled="authStore.isLoading"
                class="flex-1"
              />
              <UButton variant="outline" size="lg" :disabled="authStore.isLoading" @click="refreshCaptcha">
                <Icon name="heroicons:arrow-path" class="w-4 h-4" />
              </UButton>
            </div>
          </UFormGroup>

          <!-- 服务条款 -->
          <div class="flex items-start space-x-2">
            <UCheckbox v-model="formData.agreeToTerms" :disabled="authStore.isLoading" class="mt-1" />
            <div class="text-sm text-gray-600">
              我已阅读并同意
              <NuxtLink to="/terms" class="text-primary-600 hover:text-primary-500"> 服务条款 </NuxtLink>
              和
              <NuxtLink to="/privacy" class="text-primary-600 hover:text-primary-500"> 隐私政策 </NuxtLink>
            </div>
          </div>

          <!-- 注册按钮 -->
          <UButton type="submit" size="lg" block :loading="authStore.isLoading" :disabled="!isFormValid">
            {{ authStore.isLoading ? '注册中...' : '创建账户' }}
          </UButton>
        </UForm>
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod'
import type { RegisterForm } from '~/types'

// 页面元信息
definePageMeta({
  layout: false,
  middleware: 'guest'
})

// 页面SEO
useHead({
  title: '用户注册 - 社交购物网站',
  meta: [{ name: 'description', content: '注册社交购物账户，开启个性化购物之旅' }]
})

// 状态管理
const authStore = useAuthStore()
const toast = useToast()

// 表单数据
const formData = reactive<RegisterForm & { agreeToTerms: boolean }>({
  username: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  captcha: '',
  agreeToTerms: false
})

// 表单验证规则
const registerSchema = z
  .object({
    username: z
      .string()
      .min(3, '用户名至少3个字符')
      .max(20, '用户名最多20个字符')
      .regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'),
    email: z.string().email('请输入有效的邮箱地址'),
    phone: z
      .string()
      .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号')
      .optional()
      .or(z.literal('')),
    password: z.string().min(6, '密码至少6个字符').max(50, '密码最多50个字符'),
    confirmPassword: z.string(),
    captcha: z.string().min(4, '请输入验证码'),
    agreeToTerms: z.boolean().refine(val => val === true, '请同意服务条款和隐私政策')
  })
  .refine(data => data.password === data.confirmPassword, {
    message: '两次输入的密码不一致',
    path: ['confirmPassword']
  })

// 表单引用
const registerForm = ref()

// 密码强度
const passwordStrength = ref(0)
const passwordStrengthText = ref('')
const passwordStrengthColor = ref('bg-gray-300')
const passwordStrengthWidth = ref('0%')

// 帮助文本
const usernameHelp = '3-20个字符，只能包含字母、数字和下划线'
const passwordHelp = '至少6个字符，建议包含字母、数字和特殊字符'

// 计算属性
const isFormValid = computed(() => {
  return (
    formData.username.trim() &&
    formData.email.trim() &&
    formData.password.trim() &&
    formData.confirmPassword.trim() &&
    formData.captcha.trim() &&
    formData.agreeToTerms &&
    formData.password === formData.confirmPassword &&
    usernameStatus.value.available === true && // 确保用户名可用
    emailStatus.value.valid === true && // 确保邮箱格式正确
    emailStatus.value.available === true // 确保邮箱可用
  )
})

// 用户名检查状态
const usernameStatus = ref<{
  checking: boolean
  available: boolean | null
  message: string
}>({
  checking: false,
  available: null,
  message: ''
})

// 邮箱验证状态
const emailStatus = ref<{
  checking: boolean
  valid: boolean | null
  available: boolean | null
  message: string
}>({
  checking: false,
  valid: null,
  available: null,
  message: ''
})

// 防抖定时器
let usernameCheckTimer: NodeJS.Timeout | null = null
let emailCheckTimer: NodeJS.Timeout | null = null

// 验证用户名
const validateUsername = () => {
  const username = formData.username.trim()

  // 重置状态
  usernameStatus.value = {
    checking: false,
    available: null,
    message: ''
  }

  // 如果用户名长度不足，不进行检查
  if (username.length < 3) return

  // 清除之前的定时器
  if (usernameCheckTimer) {
    clearTimeout(usernameCheckTimer)
  }

  // 设置防抖延迟
  usernameCheckTimer = setTimeout(async () => {
    await checkUsernameAvailability(username)
  }, 500) // 500ms 防抖延迟
}

// 检查用户名是否可用
const checkUsernameAvailability = async (username: string) => {
  try {
    usernameStatus.value.checking = true

    const response = await $fetch<any>('/api/auth/check-username', {
      params: { username }
    })

    usernameStatus.value.available = response.data.available
    usernameStatus.value.message = response.data.message
  } catch (error: any) {
    console.error('检查用户名失败:', error)
    usernameStatus.value.available = false
    usernameStatus.value.message = error.data?.message || '检查用户名失败，请稍后重试'
  } finally {
    usernameStatus.value.checking = false
  }
}

// 验证邮箱
const validateEmail = () => {
  const email = formData.email.trim()

  // 重置状态
  emailStatus.value = {
    checking: false,
    valid: null,
    available: null,
    message: ''
  }

  // 如果邮箱为空，不进行检查
  if (email.length === 0) return

  // 清除之前的定时器
  if (emailCheckTimer) {
    clearTimeout(emailCheckTimer)
  }

  // 设置防抖延迟
  emailCheckTimer = setTimeout(async () => {
    await checkEmailValidityAndAvailability(email)
  }, 500) // 500ms 防抖延迟
}

// 检查邮箱格式和可用性
const checkEmailValidityAndAvailability = async (email: string) => {
  try {
    emailStatus.value.checking = true

    // 先进行基本格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      emailStatus.value.valid = false
      emailStatus.value.message = '请输入有效的邮箱地址'
      return
    }

    // 调用API检查邮箱可用性
    const response = await $fetch<any>('/api/auth/check-email', {
      params: { email }
    })

    emailStatus.value.valid = true
    emailStatus.value.available = response.data.available
    emailStatus.value.message = response.data.message
  } catch (error: any) {
    console.error('检查邮箱失败:', error)
    emailStatus.value.valid = false
    emailStatus.value.available = false
    emailStatus.value.message = error.data?.message || '检查邮箱失败，请稍后重试'
  } finally {
    emailStatus.value.checking = false
  }
}

// 检查密码强度
const checkPasswordStrength = () => {
  const password = formData.password
  let strength = 0

  if (password.length >= 6) strength += 1
  if (password.length >= 8) strength += 1
  if (/[a-z]/.test(password)) strength += 1
  if (/[A-Z]/.test(password)) strength += 1
  if (/[0-9]/.test(password)) strength += 1
  if (/[^a-zA-Z0-9]/.test(password)) strength += 1

  passwordStrength.value = strength

  if (strength <= 2) {
    passwordStrengthText.value = '弱'
    passwordStrengthColor.value = 'bg-red-500'
    passwordStrengthWidth.value = '33%'
  } else if (strength <= 4) {
    passwordStrengthText.value = '中'
    passwordStrengthColor.value = 'bg-yellow-500'
    passwordStrengthWidth.value = '66%'
  } else {
    passwordStrengthText.value = '强'
    passwordStrengthColor.value = 'bg-green-500'
    passwordStrengthWidth.value = '100%'
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  toast.add({
    title: '验证码已刷新',
    description: '请输入新的验证码',
    color: 'blue'
  })
}

// 处理注册
const handleRegister = async () => {
  try {
    const { agreeToTerms, ...registerData } = formData
    await authStore.register(registerData)

    // 注册成功后跳转到首页
    await navigateTo('/')
  } catch (error) {
    // 错误已在store中处理
    console.error('注册失败:', error)
  }
}

// 页面加载时的处理
onMounted(() => {
  // 如果已经登录，跳转到首页
  if (authStore.isLoggedIn) {
    navigateTo('/')
  }

  // 自动聚焦到用户名输入框
  nextTick(() => {
    const usernameInput = document.querySelector('input[name="username"]') as HTMLInputElement
    if (usernameInput) {
      usernameInput.focus()
    }
  })
})
</script>
