{"semi": false, "singleQuote": true, "tabWidth": 2, "useTabs": false, "trailingComma": "none", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "printWidth": 120, "quoteProps": "as-needed", "vueIndentScriptAndStyle": false, "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "proseWrap": "preserve", "insertPragma": false, "requirePragma": false, "rangeStart": 0, "rangeEnd": null}