<template>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-8">认证系统集成演示</h1>

    <!-- 用户状态展示 -->
    <UCard class="mb-8">
      <template #header>
        <h2 class="text-xl font-semibold">用户认证状态</h2>
      </template>

      <div class="space-y-4">
        <div class="flex items-center space-x-4">
          <span class="font-medium">登录状态:</span>
          <UBadge :color="authStore.isLoggedIn ? 'green' : 'red'" variant="subtle">
            {{ authStore.isLoggedIn ? '已登录' : '未登录' }}
          </UBadge>
        </div>

        <div v-if="authStore.isLoggedIn" class="space-y-2">
          <div class="flex items-center space-x-4">
            <span class="font-medium">用户信息:</span>
            <div class="flex items-center space-x-2">
              <UserAvatar
                :src="authStore.user?.avatar"
                :alt="authStore.user?.nickname || authStore.user?.username"
                size="sm"
                :role="authStore.user?.role"
                show-role-badge
              />
              <span>{{ authStore.user?.nickname || authStore.user?.username }}</span>
              <span class="text-sm text-gray-500">({{ authStore.user?.email }})</span>
            </div>
          </div>

          <div class="flex items-center space-x-4">
            <span class="font-medium">用户角色:</span>
            <UBadge :color="getRoleColor(authStore.user?.role)" variant="subtle">
              {{ getRoleText(authStore.user?.role) }}
            </UBadge>
          </div>
        </div>

        <div v-else class="flex space-x-4">
          <UButton @click="navigateTo('/login')">登录</UButton>
          <UButton variant="outline" @click="navigateTo('/register')">注册</UButton>
        </div>
      </div>
    </UCard>

    <!-- 购物车状态展示 -->
    <UCard class="mb-8">
      <template #header>
        <h2 class="text-xl font-semibold">购物车状态</h2>
      </template>

      <div class="space-y-4">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-primary-600">{{ cartStore.summary.totalItems }}</div>
            <div class="text-sm text-gray-500">商品数量</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ cartStore.summary.selectedCount }}</div>
            <div class="text-sm text-gray-500">已选择</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-red-600">¥{{ cartStore.summary.totalAmount.toFixed(2) }}</div>
            <div class="text-sm text-gray-500">总金额</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-orange-600">¥{{ cartStore.summary.discountAmount.toFixed(2) }}</div>
            <div class="text-sm text-gray-500">优惠金额</div>
          </div>
        </div>

        <div class="flex space-x-4">
          <UButton @click="navigateTo('/cart')" :disabled="cartStore.isEmpty">
            查看购物车
          </UButton>
          <UButton variant="outline" @click="navigateTo('/products')">
            去购物
          </UButton>
        </div>
      </div>
    </UCard>

    <!-- 功能演示 -->
    <UCard class="mb-8">
      <template #header>
        <h2 class="text-xl font-semibold">功能演示</h2>
      </template>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- 商品操作 -->
        <div class="space-y-4">
          <h3 class="font-semibold text-gray-900">商品操作</h3>
          <div class="space-y-2">
            <UButton
              block
              variant="outline"
              @click="testAddToCart"
              :disabled="!authStore.isLoggedIn"
            >
              <Icon name="heroicons:shopping-cart" class="w-4 h-4 mr-2" />
              添加到购物车
            </UButton>
            <UButton
              block
              variant="outline"
              @click="testToggleFavorite"
              :disabled="!authStore.isLoggedIn"
            >
              <Icon name="heroicons:heart" class="w-4 h-4 mr-2" />
              切换收藏
            </UButton>
            <UButton
              block
              variant="outline"
              @click="testShareProduct"
              :disabled="!authStore.isLoggedIn"
            >
              <Icon name="heroicons:share" class="w-4 h-4 mr-2" />
              分享商品
            </UButton>
          </div>
        </div>

        <!-- 订单操作 -->
        <div class="space-y-4">
          <h3 class="font-semibold text-gray-900">订单操作</h3>
          <div class="space-y-2">
            <UButton
              block
              variant="outline"
              @click="navigateTo('/orders')"
              :disabled="!authStore.isLoggedIn"
            >
              <Icon name="heroicons:document-text" class="w-4 h-4 mr-2" />
              查看订单
            </UButton>
            <UButton
              block
              variant="outline"
              @click="testCreateOrder"
              :disabled="!authStore.isLoggedIn || cartStore.isEmpty"
            >
              <Icon name="heroicons:credit-card" class="w-4 h-4 mr-2" />
              创建订单
            </UButton>
            <UButton
              block
              variant="outline"
              @click="testPayment"
              :disabled="!authStore.isLoggedIn"
            >
              <Icon name="heroicons:banknotes" class="w-4 h-4 mr-2" />
              支付演示
            </UButton>
          </div>
        </div>

        <!-- 社交功能 -->
        <div class="space-y-4">
          <h3 class="font-semibold text-gray-900">社交功能</h3>
          <div class="space-y-2">
            <UButton
              block
              variant="outline"
              @click="navigateTo('/social')"
            >
              <Icon name="heroicons:chat-bubble-left-right" class="w-4 h-4 mr-2" />
              社交动态
            </UButton>
            <UButton
              block
              variant="outline"
              @click="testCreatePost"
              :disabled="!authStore.isLoggedIn"
            >
              <Icon name="heroicons:pencil-square" class="w-4 h-4 mr-2" />
              发布动态
            </UButton>
            <UButton
              block
              variant="outline"
              @click="navigateTo('/notifications')"
              :disabled="!authStore.isLoggedIn"
            >
              <Icon name="heroicons:bell" class="w-4 h-4 mr-2" />
              通知中心
              <UBadge
                v-if="notificationsStore.unreadCount > 0"
                color="red"
                variant="solid"
                size="xs"
                class="ml-1"
              >
                {{ notificationsStore.unreadCount }}
              </UBadge>
            </UButton>
          </div>
        </div>

        <!-- 管理功能 -->
        <div class="space-y-4">
          <h3 class="font-semibold text-gray-900">管理功能</h3>
          <div class="space-y-2">
            <UButton
              block
              variant="outline"
              @click="navigateTo('/merchant')"
              :disabled="!authStore.hasPermission('MERCHANT')"
            >
              <Icon name="heroicons:building-storefront" class="w-4 h-4 mr-2" />
              商家管理
            </UButton>
            <UButton
              block
              variant="outline"
              @click="navigateTo('/admin')"
              :disabled="!authStore.hasPermission('ADMIN')"
            >
              <Icon name="heroicons:cog-6-tooth" class="w-4 h-4 mr-2" />
              管理员控制台
            </UButton>
            <UButton
              block
              variant="outline"
              @click="navigateTo('/profile')"
              :disabled="!authStore.isLoggedIn"
            >
              <Icon name="heroicons:user" class="w-4 h-4 mr-2" />
              个人中心
            </UButton>
          </div>
        </div>
      </div>
    </UCard>

    <!-- API请求演示 -->
    <UCard>
      <template #header>
        <h2 class="text-xl font-semibold">API请求演示</h2>
      </template>

      <div class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <UButton
            block
            variant="outline"
            @click="testPublicAPI"
            :loading="isTestingAPI"
          >
            测试公开API (商品列表)
          </UButton>
          <UButton
            block
            variant="outline"
            @click="testPrivateAPI"
            :disabled="!authStore.isLoggedIn"
            :loading="isTestingAPI"
          >
            测试私有API (用户信息)
          </UButton>
        </div>

        <div v-if="apiResult" class="mt-4">
          <h4 class="font-medium mb-2">API响应结果:</h4>
          <pre class="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto">{{ JSON.stringify(apiResult, null, 2) }}</pre>
        </div>
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
// 页面SEO
useHead({
  title: '认证系统集成演示 - 社交购物网站'
})

// 状态管理
const authStore = useAuthStore()
const cartStore = useCartStore()
const socialStore = useSocialStore()
const notificationsStore = useNotificationsStore()
const paymentStore = usePaymentStore()
const toast = useToast()

// 响应式数据
const isTestingAPI = ref(false)
const apiResult = ref(null)

// API请求实例
const api = createAuthenticatedRequest()

// 获取角色颜色
const getRoleColor = (role?: string) => {
  switch (role) {
    case 'ADMIN': return 'red'
    case 'MERCHANT': return 'blue'
    case 'USER': return 'green'
    default: return 'gray'
  }
}

// 获取角色文本
const getRoleText = (role?: string) => {
  switch (role) {
    case 'ADMIN': return '管理员'
    case 'MERCHANT': return '商家'
    case 'USER': return '用户'
    default: return '未知'
  }
}

// 测试添加到购物车
const testAddToCart = async () => {
  try {
    await cartStore.addItem(1, 1) // 假设商品ID为1
  } catch (error) {
    console.error('添加到购物车失败:', error)
  }
}

// 测试切换收藏
const testToggleFavorite = () => {
  toast.add({
    title: '功能演示',
    description: '收藏功能演示 - 需要在商品页面中使用',
    color: 'blue'
  })
}

// 测试分享商品
const testShareProduct = () => {
  toast.add({
    title: '功能演示',
    description: '分享功能演示 - 需要在商品页面中使用',
    color: 'blue'
  })
}

// 测试创建订单
const testCreateOrder = () => {
  if (cartStore.hasSelected) {
    navigateTo('/checkout')
  } else {
    toast.add({
      title: '请先选择商品',
      description: '购物车中没有选中的商品',
      color: 'yellow'
    })
  }
}

// 测试支付功能
const testPayment = () => {
  toast.add({
    title: '支付演示',
    description: '支付功能演示 - 需要在订单页面中使用',
    color: 'blue'
  })
}

// 测试发布动态
const testCreatePost = async () => {
  try {
    await socialStore.createPost({
      content: '这是一条测试动态，展示社交功能！',
      type: 'TEXT'
    })
  } catch (error) {
    console.error('发布动态失败:', error)
  }
}

// 测试刷新用户信息
const testRefreshUser = async () => {
  try {
    await authStore.refreshUser()
    toast.add({
      title: '刷新成功',
      description: '用户信息已更新',
      color: 'green'
    })
  } catch (error) {
    console.error('刷新用户信息失败:', error)
  }
}

// 测试公开API
const testPublicAPI = async () => {
  try {
    isTestingAPI.value = true
    const result = await api.get('/api/products', {
      params: { page: 1, pageSize: 5 },
      requireAuth: false
    })
    apiResult.value = result
    toast.add({
      title: 'API调用成功',
      description: '公开API调用成功',
      color: 'green'
    })
  } catch (error) {
    console.error('API调用失败:', error)
    toast.add({
      title: 'API调用失败',
      description: error.message || '请求失败',
      color: 'red'
    })
  } finally {
    isTestingAPI.value = false
  }
}

// 测试私有API
const testPrivateAPI = async () => {
  try {
    isTestingAPI.value = true
    const result = await api.get('/api/users/profile')
    apiResult.value = result
    toast.add({
      title: 'API调用成功',
      description: '私有API调用成功',
      color: 'green'
    })
  } catch (error) {
    console.error('API调用失败:', error)
    toast.add({
      title: 'API调用失败',
      description: error.message || '请求失败',
      color: 'red'
    })
  } finally {
    isTestingAPI.value = false
  }
}

// 页面加载时初始化数据
onMounted(() => {
  if (authStore.isLoggedIn) {
    cartStore.fetchCart()
  }
})
</script>
