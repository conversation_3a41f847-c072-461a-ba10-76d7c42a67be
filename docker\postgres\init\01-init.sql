-- 初始化PostgreSQL数据库
-- 创建数据库（如果不存在）
SELECT 'CREATE DATABASE social_shop'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'social_shop')\gexec

-- 创建扩展
\c social_shop;

-- 启用UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 启用全文搜索扩展
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 启用中文分词扩展（如果需要）
-- CREATE EXTENSION IF NOT EXISTS "zhparser";

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建索引函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 输出初始化完成信息
\echo '数据库初始化完成！'
