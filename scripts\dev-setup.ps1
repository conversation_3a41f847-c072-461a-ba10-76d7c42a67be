# 社交购物网站开发环境设置脚本 (Windows PowerShell)

Write-Host "🚀 开始设置开发环境..." -ForegroundColor Green

# 检查Node.js版本
Write-Host "📦 检查Node.js版本..." -ForegroundColor Yellow
try {
    $nodeVersion = node -v
    $versionNumber = [int]($nodeVersion -replace 'v(\d+)\..*', '$1')
    if ($versionNumber -lt 18) {
        Write-Host "❌ Node.js版本过低，需要18+版本，当前版本: $nodeVersion" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ Node.js版本检查通过: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js未安装，请先安装Node.js 18+版本" -ForegroundColor Red
    exit 1
}

# 检查包管理器
Write-Host "📦 检查包管理器..." -ForegroundColor Yellow
$pkgManager = "npm"
if (Get-Command pnpm -ErrorAction SilentlyContinue) {
    $pkgManager = "pnpm"
} elseif (Get-Command yarn -ErrorAction SilentlyContinue) {
    $pkgManager = "yarn"
}
Write-Host "✅ 使用包管理器: $pkgManager" -ForegroundColor Green

# 安装依赖
Write-Host "📦 安装项目依赖..." -ForegroundColor Yellow
& $pkgManager install

# 检查Docker
Write-Host "🐳 检查Docker环境..." -ForegroundColor Yellow
if ((Get-Command docker -ErrorAction SilentlyContinue) -and (Get-Command docker-compose -ErrorAction SilentlyContinue)) {
    Write-Host "✅ Docker环境可用" -ForegroundColor Green
    
    # 启动开发服务
    Write-Host "🚀 启动开发服务..." -ForegroundColor Yellow
    docker-compose -f docker-compose.dev.yml up -d
    
    # 等待服务启动
    Write-Host "⏳ 等待服务启动..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    
    # 检查服务状态
    Write-Host "🔍 检查服务状态..." -ForegroundColor Yellow
    docker-compose -f docker-compose.dev.yml ps
} else {
    Write-Host "⚠️  Docker未安装，请手动配置PostgreSQL和Redis" -ForegroundColor Yellow
}

# 复制环境变量文件
Write-Host "⚙️  配置环境变量..." -ForegroundColor Yellow
if (-not (Test-Path .env)) {
    Copy-Item .env.example .env
    Write-Host "✅ 已创建.env文件，请根据需要修改配置" -ForegroundColor Green
} else {
    Write-Host "✅ .env文件已存在" -ForegroundColor Green
}

# 设置Git hooks
Write-Host "🔧 设置Git hooks..." -ForegroundColor Yellow
if (Test-Path .git) {
    & $pkgManager run prepare
    Write-Host "✅ Git hooks设置完成" -ForegroundColor Green
} else {
    Write-Host "⚠️  不是Git仓库，跳过Git hooks设置" -ForegroundColor Yellow
}

# 生成Prisma客户端
Write-Host "🗄️  生成Prisma客户端..." -ForegroundColor Yellow
& $pkgManager run db:generate

# 运行数据库迁移
Write-Host "🗄️  运行数据库迁移..." -ForegroundColor Yellow
try {
    $dockerStatus = docker-compose -f docker-compose.dev.yml ps postgres
    if ($dockerStatus -match "Up") {
        Write-Host "⏳ 等待数据库就绪..." -ForegroundColor Yellow
        Start-Sleep -Seconds 5
        & $pkgManager run db:migrate
        
        # 填充测试数据
        Write-Host "🌱 填充测试数据..." -ForegroundColor Yellow
        & $pkgManager run db:seed
        Write-Host "✅ 测试数据填充完成" -ForegroundColor Green
    } else {
        Write-Host "⚠️  数据库未启动，请手动运行迁移: npm run db:migrate" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  数据库未启动，请手动运行迁移: npm run db:migrate" -ForegroundColor Yellow
}

# 运行代码检查
Write-Host "🔍 运行代码检查..." -ForegroundColor Yellow
& $pkgManager run lint
& $pkgManager run type-check

Write-Host ""
Write-Host "🎉 开发环境设置完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📋 可用的命令:" -ForegroundColor Cyan
Write-Host "  $pkgManager run dev          - 启动开发服务器"
Write-Host "  $pkgManager run db:studio    - 打开Prisma Studio"
Write-Host "  $pkgManager run lint         - 运行代码检查"
Write-Host "  $pkgManager run format       - 格式化代码"
Write-Host "  $pkgManager run type-check   - 类型检查"
Write-Host ""
Write-Host "🌐 服务地址:" -ForegroundColor Cyan
Write-Host "  应用: http://localhost:3000"
Write-Host "  pgAdmin: http://localhost:5050 (<EMAIL> / admin123)"
Write-Host "  Redis Commander: http://localhost:8081 (admin / admin123)"
Write-Host "  MailHog: http://localhost:8025"
Write-Host ""
Write-Host "🔑 测试账号:" -ForegroundColor Cyan
Write-Host "  管理员: <EMAIL> / 123456"
Write-Host "  商家: <EMAIL> / 123456"
Write-Host "  用户: <EMAIL> / 123456"
Write-Host ""
Write-Host "🚀 运行 '$pkgManager run dev' 启动开发服务器" -ForegroundColor Green
