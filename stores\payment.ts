import type { PaymentRequest, PaymentResponse } from '~/types'

/**
 * 支付系统状态管理
 */
export const usePaymentStore = defineStore('payment', () => {
  // 状态
  const currentPayment = ref<PaymentResponse | null>(null)
  const paymentHistory = ref<PaymentResponse[]>([])
  const isProcessing = ref(false)
  const supportedMethods = ref<Array<{
    id: string
    name: string
    icon: string
    enabled: boolean
  }>>([
    { id: 'alipay', name: '支付宝', icon: 'alipay', enabled: true },
    { id: 'wechat', name: '微信支付', icon: 'wechat', enabled: true },
    { id: 'unionpay', name: '银联支付', icon: 'unionpay', enabled: false }
  ])

  // API实例
  const request = createAuthenticatedRequest()
  const authStore = useAuthStore()
  const toast = useToast()

  // 创建支付订单
  const createPayment = async (paymentData: PaymentRequest) => {
    if (!authStore.isLoggedIn) {
      toast.add({
        title: '请先登录',
        description: '登录后即可进行支付',
        color: 'yellow'
      })
      return navigateTo('/login')
    }

    try {
      isProcessing.value = true
      
      const payment = await request.post<PaymentResponse>('/api/payments', paymentData)
      currentPayment.value = payment
      
      return payment
    } catch (error: any) {
      console.error('创建支付订单失败:', error)
      toast.add({
        title: '支付失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    } finally {
      isProcessing.value = false
    }
  }

  // 查询支付状态
  const queryPaymentStatus = async (paymentId: string) => {
    try {
      const payment = await request.get<PaymentResponse>(`/api/payments/${paymentId}/status`)
      
      // 更新当前支付状态
      if (currentPayment.value?.paymentId === paymentId) {
        currentPayment.value = payment
      }
      
      return payment
    } catch (error: any) {
      console.error('查询支付状态失败:', error)
      throw error
    }
  }

  // 轮询支付状态
  const pollPaymentStatus = (paymentId: string, maxAttempts: number = 60) => {
    return new Promise<PaymentResponse>((resolve, reject) => {
      let attempts = 0
      
      const poll = async () => {
        try {
          attempts++
          const payment = await queryPaymentStatus(paymentId)
          
          if (payment.status === 'success') {
            resolve(payment)
          } else if (payment.status === 'failed') {
            reject(new Error('支付失败'))
          } else if (attempts >= maxAttempts) {
            reject(new Error('支付超时'))
          } else {
            // 继续轮询
            setTimeout(poll, 2000)
          }
        } catch (error) {
          reject(error)
        }
      }
      
      poll()
    })
  }

  // 处理支付结果
  const handlePaymentResult = async (paymentId: string, status: 'success' | 'failed' | 'cancelled') => {
    try {
      const payment = await queryPaymentStatus(paymentId)
      
      if (status === 'success') {
        toast.add({
          title: '支付成功',
          description: '订单支付完成',
          color: 'green'
        })
        
        // 跳转到订单详情页面
        const orderId = payment.paymentId // 假设paymentId包含订单信息
        await navigateTo(`/orders/${orderId}`)
      } else if (status === 'failed') {
        toast.add({
          title: '支付失败',
          description: '请重新尝试支付',
          color: 'red'
        })
      } else {
        toast.add({
          title: '支付已取消',
          color: 'yellow'
        })
      }
      
      return payment
    } catch (error: any) {
      console.error('处理支付结果失败:', error)
      throw error
    }
  }

  // 获取支付历史
  const fetchPaymentHistory = async () => {
    if (!authStore.isLoggedIn) return

    try {
      const payments = await request.get<PaymentResponse[]>('/api/payments/history')
      paymentHistory.value = payments
      return payments
    } catch (error: any) {
      console.error('获取支付历史失败:', error)
      throw error
    }
  }

  // 申请退款
  const requestRefund = async (paymentId: string, reason: string, amount?: number) => {
    if (!authStore.isLoggedIn) return

    try {
      const refund = await request.post(`/api/payments/${paymentId}/refund`, {
        reason,
        amount
      })
      
      toast.add({
        title: '退款申请已提交',
        description: '我们会尽快处理您的退款申请',
        color: 'blue'
      })
      
      return refund
    } catch (error: any) {
      console.error('申请退款失败:', error)
      toast.add({
        title: '退款申请失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 验证支付参数
  const validatePaymentData = (paymentData: PaymentRequest): string[] => {
    const errors: string[] = []
    
    if (!paymentData.orderId) {
      errors.push('订单ID不能为空')
    }
    
    if (!paymentData.paymentMethod) {
      errors.push('请选择支付方式')
    }
    
    const method = supportedMethods.value.find(m => m.id === paymentData.paymentMethod)
    if (!method || !method.enabled) {
      errors.push('不支持的支付方式')
    }
    
    return errors
  }

  // 获取支付方式图标
  const getPaymentMethodIcon = (method: string) => {
    const icons = {
      'alipay': 'logos:alipay',
      'wechat': 'logos:wechat',
      'unionpay': 'logos:unionpay'
    }
    return icons[method as keyof typeof icons] || 'heroicons:credit-card'
  }

  // 获取支付状态文本
  const getPaymentStatusText = (status: string) => {
    const texts = {
      'pending': '待支付',
      'success': '支付成功',
      'failed': '支付失败',
      'cancelled': '已取消',
      'refunded': '已退款'
    }
    return texts[status as keyof typeof texts] || status
  }

  // 获取支付状态颜色
  const getPaymentStatusColor = (status: string) => {
    const colors = {
      'pending': 'yellow',
      'success': 'green',
      'failed': 'red',
      'cancelled': 'gray',
      'refunded': 'blue'
    }
    return colors[status as keyof typeof colors] || 'gray'
  }

  // 格式化金额
  const formatAmount = (amount: number) => {
    return `¥${amount.toFixed(2)}`
  }

  // 生成支付二维码
  const generateQRCode = async (paymentUrl: string) => {
    try {
      // TODO: 集成二维码生成库
      return paymentUrl
    } catch (error) {
      console.error('生成二维码失败:', error)
      return null
    }
  }

  // 检查支付环境
  const checkPaymentEnvironment = () => {
    const userAgent = navigator.userAgent.toLowerCase()
    
    return {
      isWechat: /micromessenger/.test(userAgent),
      isAlipay: /alipayclient/.test(userAgent),
      isMobile: /mobile|android|iphone|ipad/.test(userAgent),
      isDesktop: !/mobile|android|iphone|ipad/.test(userAgent)
    }
  }

  // 获取推荐支付方式
  const getRecommendedPaymentMethod = () => {
    const env = checkPaymentEnvironment()
    
    if (env.isWechat) {
      return 'wechat'
    } else if (env.isAlipay) {
      return 'alipay'
    } else if (env.isMobile) {
      return 'alipay' // 移动端默认推荐支付宝
    } else {
      return 'alipay' // 桌面端默认推荐支付宝
    }
  }

  // 清空状态
  const clearState = () => {
    currentPayment.value = null
    paymentHistory.value = []
    isProcessing.value = false
  }

  return {
    // 状态
    currentPayment: readonly(currentPayment),
    paymentHistory: readonly(paymentHistory),
    isProcessing: readonly(isProcessing),
    supportedMethods: readonly(supportedMethods),
    
    // 方法
    createPayment,
    queryPaymentStatus,
    pollPaymentStatus,
    handlePaymentResult,
    fetchPaymentHistory,
    requestRefund,
    validatePaymentData,
    
    // 工具方法
    getPaymentMethodIcon,
    getPaymentStatusText,
    getPaymentStatusColor,
    formatAmount,
    generateQRCode,
    checkPaymentEnvironment,
    getRecommendedPaymentMethod,
    clearState
  }
})
