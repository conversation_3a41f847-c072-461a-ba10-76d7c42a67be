-- CreateEnum
CREATE TYPE "UserLogAction" AS ENUM ('<PERSON><PERSON><PERSON><PERSON>', 'LOGOUT', 'REGISTER', 'PASSWORD_CHANGE', 'PROFILE_UPDATE', 'PRODUCT_VIEW', 'PRODUCT_SEARCH', 'ORDER_CREATE', 'ORDER_PAY', 'OR<PERSON><PERSON>_CANCEL', 'CART_ADD', 'CART_REMOVE', 'FAVORITE_ADD', 'FAVORITE_REMOVE', 'REVIEW_CREATE', 'COUPON_CLAIM', 'COUPON_USE', 'POST_CREATE', 'POST_LIKE', 'COMMENT_CREATE', 'FOLLOW_USER', 'UNFOLLOW_USER');

-- CreateTable
CREATE TABLE "search_logs" (
    "id" SERIAL NOT NULL,
    "keyword" VARCHAR(255) NOT NULL,
    "result_count" INTEGER NOT NULL,
    "user_id" INTEGER,
    "user_agent" TEXT,
    "ip_address" VARCHAR(45),
    "filters" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "search_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_logs" (
    "id" SERIAL NOT NULL,
    "user_id" INTEGER,
    "action" "UserLogAction" NOT NULL,
    "resource" VARCHAR(100),
    "resource_id" INTEGER,
    "details" JSONB,
    "user_agent" TEXT,
    "ip_address" VARCHAR(45),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_logs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "search_logs_keyword_idx" ON "search_logs"("keyword");

-- CreateIndex
CREATE INDEX "search_logs_user_id_idx" ON "search_logs"("user_id");

-- CreateIndex
CREATE INDEX "search_logs_created_at_idx" ON "search_logs"("created_at");

-- CreateIndex
CREATE INDEX "user_logs_user_id_idx" ON "user_logs"("user_id");

-- CreateIndex
CREATE INDEX "user_logs_action_idx" ON "user_logs"("action");

-- CreateIndex
CREATE INDEX "user_logs_resource_resource_id_idx" ON "user_logs"("resource", "resource_id");

-- CreateIndex
CREATE INDEX "user_logs_created_at_idx" ON "user_logs"("created_at");

-- AddForeignKey
ALTER TABLE "search_logs" ADD CONSTRAINT "search_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_logs" ADD CONSTRAINT "user_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
