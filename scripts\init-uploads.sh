#!/bin/bash

# 创建上传目录脚本
echo "📁 创建上传目录..."

# 创建上传目录
mkdir -p public/uploads/images
mkdir -p public/uploads/avatars
mkdir -p public/uploads/documents

# 设置目录权限（如果在Linux/macOS上）
if [[ "$OSTYPE" == "linux-gnu"* ]] || [[ "$OSTYPE" == "darwin"* ]]; then
    chmod 755 public/uploads
    chmod 755 public/uploads/images
    chmod 755 public/uploads/avatars
    chmod 755 public/uploads/documents
fi

# 创建.gitkeep文件以确保空目录被Git跟踪
touch public/uploads/images/.gitkeep
touch public/uploads/avatars/.gitkeep
touch public/uploads/documents/.gitkeep

echo "✅ 上传目录创建完成！"
echo ""
echo "📂 创建的目录："
echo "  - public/uploads/images/     (图片上传)"
echo "  - public/uploads/avatars/    (头像上传)"
echo "  - public/uploads/documents/  (文档上传)"
echo ""
echo "🔒 目录权限已设置为 755"
echo "📝 已创建 .gitkeep 文件确保目录被Git跟踪"
