# ⚙️ 配置指南

本文档详细说明了项目的配置选项和最佳实践。

## 📋 环境变量配置

### 必需配置

以下配置项是项目运行的必需项：

#### 数据库配置

```env
# PostgreSQL 数据库连接字符串
DATABASE_URL="postgresql://用户名:密码@主机:端口/数据库名"

# 分离式数据库配置（可选，用于更灵活的配置）
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=shop_db
```

**配置说明：**

- `DATABASE_URL`: 完整的数据库连接字符串，Prisma 使用此配置连接数据库
- `DB_HOST`: 数据库服务器地址
- `DB_PORT`: 数据库端口，PostgreSQL 默认为 5432
- `DB_USER`: 数据库用户名
- `DB_PASSWORD`: 数据库密码
- `DB_NAME`: 数据库名称

#### JWT 配置

```env
# JWT 密钥，用于生成和验证用户令牌
JWT_SECRET="your-super-secret-jwt-key-here-please-change-in-production"
```

**安全建议：**

- 生产环境必须使用强密码（至少 32 个字符）
- 建议使用随机生成的字符串
- 不要在代码中硬编码 JWT 密钥

### 可选配置

#### Redis 配置

```env
# Redis 连接字符串（用于缓存和会话存储）
REDIS_URL="redis://localhost:6379"

# 带密码的 Redis 连接
REDIS_URL="redis://:password@localhost:6379"
```

#### 应用配置

```env
# 应用运行端口
PORT=3000

# 应用基础URL
BASE_URL="http://localhost:3000"

# 调试模式
DEBUG=true

# 日志级别
LOG_LEVEL="info"
```

#### 文件上传配置

```env
# 上传目录
UPLOAD_DIR=./public/uploads

# 最大文件大小（字节）
MAX_FILE_SIZE=5242880  # 5MB

# 图片上传最大大小
MAX_IMAGE_SIZE=5242880  # 5MB

# 头像上传最大大小
MAX_AVATAR_SIZE=2097152  # 2MB
```

#### 邮件服务配置

```env
# SMTP 服务器配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
SMTP_FROM="Shop <<EMAIL>>"
```

#### 支付服务配置

```env
# 支付宝配置
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY=your_alipay_private_key

# 微信支付配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret
```

## 🛠️ 配置方法

### 方法一：使用初始化脚本（推荐）

```bash
# 交互式配置
npm run setup

# 静默模式（使用默认配置）
npm run setup:silent

# 跳过配置询问
npm run setup:skip
```

### 方法二：手动配置

1. **复制配置文件**

   ```bash
   cp .env.example .env
   ```

2. **编辑配置文件**

   ```bash
   # 使用你喜欢的编辑器
   nano .env
   # 或
   code .env
   ```

3. **修改必要配置**
   - 更新 `DATABASE_URL` 为你的数据库连接信息
   - 更改 `JWT_SECRET` 为强密码
   - 根据需要配置其他选项

## 🔍 配置验证

### 自动验证

初始化脚本会自动验证以下配置：

- ✅ `.env` 文件是否存在
- ✅ `DATABASE_URL` 是否配置
- ✅ `JWT_SECRET` 是否配置
- ✅ 数据库连接是否正常

### 手动验证

```bash
# 检查配置文件
cat .env

# 测试数据库连接
npx prisma db push --preview-feature

# 验证 JWT 密钥长度
node -e "console.log(process.env.JWT_SECRET?.length || 0)"
```

## 🌍 环境特定配置

### 开发环境

```env
NODE_ENV=development
DEBUG=true
LOG_LEVEL=debug
BASE_URL=http://localhost:3000
```

### 生产环境

```env
NODE_ENV=production
DEBUG=false
LOG_LEVEL=error
BASE_URL=https://yourdomain.com
# 使用强 JWT 密钥
JWT_SECRET=your-production-jwt-secret-at-least-32-characters-long
```

### 测试环境

```env
NODE_ENV=test
DATABASE_URL=postgresql://postgres:password@localhost:5432/shop_test
JWT_SECRET=test-jwt-secret-for-testing-only
```

## 🔒 安全最佳实践

### 1. 密钥管理

- 生产环境使用环境变量或密钥管理服务
- 不要在代码仓库中提交真实的密钥
- 定期轮换密钥

### 2. 数据库安全

- 使用专用数据库用户，限制权限
- 启用数据库连接加密
- 定期备份数据库

### 3. 网络安全

- 生产环境使用 HTTPS
- 配置防火墙规则
- 使用反向代理

## 🔧 故障排除

### 常见问题

**1. 数据库连接失败**

```
Error: P1001: Can't reach database server
```

**解决方案：**

- 检查数据库服务是否启动
  ```bash
  # PostgreSQL 服务状态检查
  sudo systemctl status postgresql    # Linux
  brew services list | grep postgres  # macOS
  ```
- 验证连接字符串格式

  ```env
  # 正确格式
  DATABASE_URL="postgresql://用户名:密码@主机:端口/数据库名"

  # 示例
  DATABASE_URL="postgresql://postgres:password@localhost:5432/shop_db"
  ```

- 确认网络连接
  ```bash
  # 测试端口连通性
  nc -zv localhost 5432
  # 或
  telnet localhost 5432
  ```
- 检查数据库用户权限

  ```sql
  -- 连接到PostgreSQL
  psql -U postgres

  -- 检查用户权限
  \du

  -- 授予权限
  GRANT ALL PRIVILEGES ON DATABASE shop_db TO your_user;
  ```

**2. JWT 密钥错误**

```
Error: JWT secret is required
```

**解决方案：**

- 检查 `.env` 文件中的 `JWT_SECRET` 配置
- 确保密钥长度足够（建议 32+ 字符）

**3. 端口被占用**

```
Error: listen EADDRINUSE :::3000
```

**解决方案：**

- 修改 `PORT` 环境变量
- 或停止占用端口的其他服务

### 配置检查清单

- [ ] `.env` 文件已创建
- [ ] `DATABASE_URL` 已正确配置
- [ ] `JWT_SECRET` 已设置强密码
- [ ] 数据库服务正在运行
- [ ] 数据库用户有足够权限
- [ ] 防火墙允许数据库连接
- [ ] Redis 服务正在运行（如果使用）

## 📞 获取帮助

如果遇到配置问题：

1. 查看错误日志
2. 检查配置文件语法
3. 验证服务状态
4. 参考本文档的故障排除部分
5. 提交 Issue 或联系技术支持

---

**提示：** 配置完成后，建议运行 `npm run setup` 验证所有配置是否正确。
