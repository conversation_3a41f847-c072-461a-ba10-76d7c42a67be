<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">商家管理中心</h1>
        <p class="text-gray-600 mt-2">管理您的商品、订单和店铺数据</p>
      </div>
      
      <div class="flex items-center space-x-4">
        <UButton variant="outline" @click="merchantStore.exportData('products')">
          <Icon name="heroicons:arrow-down-tray" class="w-4 h-4 mr-2" />
          导出数据
        </UButton>
        <UButton @click="navigateTo('/merchant/products/create')">
          <Icon name="heroicons:plus" class="w-4 h-4 mr-2" />
          添加商品
        </UButton>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <UCard class="p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">总商品数</p>
            <p class="text-2xl font-bold text-gray-900">{{ merchantStore.statistics.totalProducts }}</p>
          </div>
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <Icon name="heroicons:shopping-bag" class="w-6 h-6 text-blue-600" />
          </div>
        </div>
      </UCard>

      <UCard class="p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">总订单数</p>
            <p class="text-2xl font-bold text-gray-900">{{ merchantStore.statistics.totalOrders }}</p>
          </div>
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <Icon name="heroicons:document-text" class="w-6 h-6 text-green-600" />
          </div>
        </div>
      </UCard>

      <UCard class="p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">总收入</p>
            <p class="text-2xl font-bold text-gray-900">¥{{ merchantStore.statistics.totalRevenue.toLocaleString() }}</p>
          </div>
          <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
            <Icon name="heroicons:currency-dollar" class="w-6 h-6 text-yellow-600" />
          </div>
        </div>
      </UCard>

      <UCard class="p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">总客户数</p>
            <p class="text-2xl font-bold text-gray-900">{{ merchantStore.statistics.totalCustomers }}</p>
          </div>
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
            <Icon name="heroicons:users" class="w-6 h-6 text-purple-600" />
          </div>
        </div>
      </UCard>
    </div>

    <!-- 今日数据 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <UCard class="p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">今日订单</p>
            <p class="text-xl font-semibold text-gray-900">{{ merchantStore.statistics.todayOrders }}</p>
          </div>
          <UBadge color="blue" variant="subtle">今日</UBadge>
        </div>
      </UCard>

      <UCard class="p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">今日收入</p>
            <p class="text-xl font-semibold text-gray-900">¥{{ merchantStore.statistics.todayRevenue.toLocaleString() }}</p>
          </div>
          <UBadge color="green" variant="subtle">今日</UBadge>
        </div>
      </UCard>

      <UCard class="p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">待处理订单</p>
            <p class="text-xl font-semibold text-gray-900">{{ merchantStore.statistics.pendingOrders }}</p>
          </div>
          <UBadge color="yellow" variant="subtle">待处理</UBadge>
        </div>
      </UCard>

      <UCard class="p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">低库存商品</p>
            <p class="text-xl font-semibold text-gray-900">{{ merchantStore.statistics.lowStockProducts }}</p>
          </div>
          <UBadge color="red" variant="subtle">警告</UBadge>
        </div>
      </UCard>
    </div>

    <!-- 快捷操作 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <!-- 待处理订单 -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold">待处理订单</h3>
            <UButton variant="ghost" size="sm" @click="navigateTo('/merchant/orders?status=PENDING')">
              查看全部
            </UButton>
          </div>
        </template>
        
        <div class="space-y-4">
          <div 
            v-for="order in merchantStore.pendingOrders.slice(0, 5)"
            :key="order.id"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          >
            <div>
              <div class="font-medium">订单 #{{ order.orderNo }}</div>
              <div class="text-sm text-gray-500">¥{{ order.paymentAmount }} · {{ formatDate(order.createdAt) }}</div>
            </div>
            <UButton size="sm" @click="processOrder(order.id, 'CONFIRM')">
              确认订单
            </UButton>
          </div>
          
          <div v-if="merchantStore.pendingOrders.length === 0" class="text-center py-4 text-gray-500">
            暂无待处理订单
          </div>
        </div>
      </UCard>

      <!-- 低库存商品 -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold">低库存商品</h3>
            <UButton variant="ghost" size="sm" @click="navigateTo('/merchant/products?filter=low-stock')">
              查看全部
            </UButton>
          </div>
        </template>
        
        <div class="space-y-4">
          <div 
            v-for="product in merchantStore.lowStockProducts.slice(0, 5)"
            :key="product.id"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          >
            <div class="flex items-center space-x-3">
              <img 
                :src="product.images[0] || '/images/placeholder.jpg'"
                :alt="product.name"
                class="w-10 h-10 object-cover rounded"
              >
              <div>
                <div class="font-medium">{{ product.name }}</div>
                <div class="text-sm text-red-600">库存：{{ product.stock }}</div>
              </div>
            </div>
            <UButton size="sm" variant="outline" @click="navigateTo(`/merchant/products/${product.id}/edit`)">
              补货
            </UButton>
          </div>
          
          <div v-if="merchantStore.lowStockProducts.length === 0" class="text-center py-4 text-gray-500">
            库存充足
          </div>
        </div>
      </UCard>
    </div>

    <!-- 快捷导航 -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
      <UButton
        variant="outline"
        size="lg"
        block
        @click="navigateTo('/merchant/products')"
        class="h-20 flex-col"
      >
        <Icon name="heroicons:shopping-bag" class="w-6 h-6 mb-2" />
        商品管理
      </UButton>

      <UButton
        variant="outline"
        size="lg"
        block
        @click="navigateTo('/merchant/orders')"
        class="h-20 flex-col"
      >
        <Icon name="heroicons:document-text" class="w-6 h-6 mb-2" />
        订单管理
      </UButton>

      <UButton
        variant="outline"
        size="lg"
        block
        @click="navigateTo('/merchant/analytics')"
        class="h-20 flex-col"
      >
        <Icon name="heroicons:chart-bar" class="w-6 h-6 mb-2" />
        数据分析
      </UButton>

      <UButton
        variant="outline"
        size="lg"
        block
        @click="navigateTo('/merchant/settings')"
        class="h-20 flex-col"
      >
        <Icon name="heroicons:cog-6-tooth" class="w-6 h-6 mb-2" />
        店铺设置
      </UButton>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元信息
definePageMeta({
  middleware: ['auth', 'merchant']
})

// 页面SEO
useHead({
  title: '商家管理中心 - 社交购物网站'
})

// 状态管理
const merchantStore = useMerchantStore()
const toast = useToast()

// 格式化日期
const formatDate = (date: string | Date) => {
  return new Date(date).toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 处理订单
const processOrder = async (orderId: number, action: 'CONFIRM' | 'SHIP' | 'CANCEL') => {
  try {
    await merchantStore.processOrder(orderId, action)
    // 刷新统计数据
    await merchantStore.fetchStatistics()
  } catch (error) {
    console.error('处理订单失败:', error)
  }
}

// 页面加载时获取数据
onMounted(async () => {
  try {
    await Promise.all([
      merchantStore.fetchStatistics(),
      merchantStore.fetchProducts({ pageSize: 5 }),
      merchantStore.fetchOrders({ pageSize: 5, status: 'PENDING' })
    ])
  } catch (error) {
    console.error('加载数据失败:', error)
  }
})
</script>
