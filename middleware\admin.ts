/**
 * 管理员权限中间件
 * 只允许管理员访问
 */

export default defineNuxtRouteMiddleware(() => {
  const authStore = useAuthStore()
  
  // 检查是否已登录
  if (!authStore.isLoggedIn) {
    throw createError({
      statusCode: 401,
      statusMessage: '请先登录'
    })
  }
  
  // 检查是否有管理员权限
  if (!authStore.hasPermission('ADMIN')) {
    throw createError({
      statusCode: 403,
      statusMessage: '需要管理员权限才能访问此页面'
    })
  }
})
