<template>
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-8">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">订单支付</h1>
        <p class="text-gray-600">请选择支付方式完成订单支付</p>
      </div>

      <!-- 订单信息 -->
      <UCard v-if="order" class="mb-6">
        <template #header>
          <h3 class="text-lg font-semibold">订单信息</h3>
        </template>
        
        <div class="space-y-4">
          <div class="flex justify-between">
            <span class="text-gray-600">订单号：</span>
            <span class="font-medium">{{ order.orderNo }}</span>
          </div>
          
          <div class="flex justify-between">
            <span class="text-gray-600">订单金额：</span>
            <span class="text-2xl font-bold text-red-600">{{ paymentStore.formatAmount(order.paymentAmount) }}</span>
          </div>
          
          <div class="flex justify-between">
            <span class="text-gray-600">创建时间：</span>
            <span>{{ formatDate(order.createdAt) }}</span>
          </div>
          
          <!-- 商品列表 -->
          <div class="border-t pt-4">
            <h4 class="font-medium mb-3">商品清单</h4>
            <div class="space-y-3">
              <div 
                v-for="item in order.items"
                :key="item.id"
                class="flex items-center space-x-3"
              >
                <img 
                  :src="item.productImage || '/images/placeholder.jpg'"
                  :alt="item.productName"
                  class="w-12 h-12 object-cover rounded"
                >
                <div class="flex-1">
                  <div class="font-medium">{{ item.productName }}</div>
                  <div class="text-sm text-gray-500">¥{{ item.price }} × {{ item.quantity }}</div>
                </div>
                <div class="font-medium">¥{{ item.totalAmount }}</div>
              </div>
            </div>
          </div>
        </div>
      </UCard>

      <!-- 支付方式选择 -->
      <UCard class="mb-6">
        <template #header>
          <h3 class="text-lg font-semibold">选择支付方式</h3>
        </template>
        
        <div class="space-y-3">
          <div
            v-for="method in availablePaymentMethods"
            :key="method.id"
            @click="selectedPaymentMethod = method.id"
            class="flex items-center space-x-4 p-4 border rounded-lg cursor-pointer transition-colors"
            :class="{
              'border-primary-500 bg-primary-50': selectedPaymentMethod === method.id,
              'border-gray-200 hover:bg-gray-50': selectedPaymentMethod !== method.id
            }"
          >
            <URadio
              :model-value="selectedPaymentMethod"
              :value="method.id"
              @update:model-value="selectedPaymentMethod = $event"
            />
            
            <Icon 
              :name="paymentStore.getPaymentMethodIcon(method.id)"
              class="w-8 h-8"
            />
            
            <div class="flex-1">
              <div class="font-medium">{{ method.name }}</div>
              <div class="text-sm text-gray-500">安全快捷的在线支付</div>
            </div>
            
            <UBadge v-if="method.id === recommendedMethod" color="green" variant="subtle">
              推荐
            </UBadge>
          </div>
        </div>
      </UCard>

      <!-- 支付按钮 -->
      <div class="space-y-4">
        <UButton
          size="lg"
          block
          @click="handlePayment"
          :loading="paymentStore.isProcessing"
          :disabled="!selectedPaymentMethod || !order"
        >
          <Icon name="heroicons:credit-card" class="w-5 h-5 mr-2" />
          立即支付 {{ order ? paymentStore.formatAmount(order.paymentAmount) : '' }}
        </UButton>
        
        <div class="text-center">
          <UButton variant="ghost" @click="navigateTo(`/orders/${orderId}`)">
            返回订单详情
          </UButton>
        </div>
      </div>

      <!-- 支付安全提示 -->
      <div class="mt-8 p-4 bg-blue-50 rounded-lg">
        <div class="flex items-start space-x-3">
          <Icon name="heroicons:shield-check" class="w-5 h-5 text-blue-600 mt-0.5" />
          <div class="text-sm text-blue-800">
            <div class="font-medium mb-1">支付安全保障</div>
            <ul class="space-y-1 text-blue-700">
              <li>• 采用银行级SSL加密技术</li>
              <li>• 支持7天无理由退款</li>
              <li>• 24小时客服支持</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- 支付结果模态框 -->
    <UModal v-model="showPaymentModal" :prevent-close="true">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">支付处理中</h3>
        </template>
        
        <div class="text-center py-8">
          <div v-if="paymentResult === 'processing'" class="space-y-4">
            <div class="w-16 h-16 mx-auto">
              <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-600"></div>
            </div>
            <div>
              <div class="font-medium mb-2">正在处理支付...</div>
              <div class="text-sm text-gray-500">请在支付页面完成支付，支付完成后会自动跳转</div>
            </div>
          </div>
          
          <div v-else-if="paymentResult === 'success'" class="space-y-4">
            <Icon name="heroicons:check-circle" class="w-16 h-16 text-green-600 mx-auto" />
            <div>
              <div class="font-medium text-green-600 mb-2">支付成功！</div>
              <div class="text-sm text-gray-500">订单支付完成，正在跳转到订单详情...</div>
            </div>
          </div>
          
          <div v-else-if="paymentResult === 'failed'" class="space-y-4">
            <Icon name="heroicons:x-circle" class="w-16 h-16 text-red-600 mx-auto" />
            <div>
              <div class="font-medium text-red-600 mb-2">支付失败</div>
              <div class="text-sm text-gray-500 mb-4">{{ paymentError || '支付过程中出现错误，请重试' }}</div>
              <UButton @click="retryPayment">重新支付</UButton>
            </div>
          </div>
        </div>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import type { Order } from '~/types'

// 页面元信息
definePageMeta({
  middleware: 'auth'
})

// 页面参数
const route = useRoute()
const orderId = parseInt(route.params.orderId as string)

// 页面SEO
useHead(() => ({
  title: `订单支付 - ${order.value?.orderNo || ''} - 社交购物网站`
}))

// 状态管理
const paymentStore = usePaymentStore()
const ordersStore = useOrdersStore()
const toast = useToast()

// 响应式数据
const order = ref<Order | null>(null)
const selectedPaymentMethod = ref('')
const showPaymentModal = ref(false)
const paymentResult = ref<'processing' | 'success' | 'failed' | null>(null)
const paymentError = ref('')

// 计算属性
const availablePaymentMethods = computed(() => 
  paymentStore.supportedMethods.filter(method => method.enabled)
)

const recommendedMethod = computed(() => 
  paymentStore.getRecommendedPaymentMethod()
)

// 格式化日期
const formatDate = (date: string | Date) => {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 处理支付
const handlePayment = async () => {
  if (!order.value || !selectedPaymentMethod.value) return

  try {
    // 验证支付数据
    const paymentData = {
      orderId: order.value.id,
      paymentMethod: selectedPaymentMethod.value as 'alipay' | 'wechat',
      returnUrl: `${window.location.origin}/payment/callback`
    }

    const errors = paymentStore.validatePaymentData(paymentData)
    if (errors.length > 0) {
      toast.add({
        title: '支付参数错误',
        description: errors.join(', '),
        color: 'red'
      })
      return
    }

    // 创建支付订单
    const payment = await paymentStore.createPayment(paymentData)
    
    if (payment.paymentUrl) {
      // 显示支付处理模态框
      showPaymentModal.value = true
      paymentResult.value = 'processing'
      
      // 跳转到支付页面
      window.open(payment.paymentUrl, '_blank')
      
      // 开始轮询支付状态
      try {
        const result = await paymentStore.pollPaymentStatus(payment.paymentId)
        paymentResult.value = 'success'
        
        // 延迟跳转
        setTimeout(() => {
          navigateTo(`/orders/${orderId}`)
        }, 2000)
      } catch (error: any) {
        paymentResult.value = 'failed'
        paymentError.value = error.message
      }
    }
  } catch (error: any) {
    console.error('支付失败:', error)
    paymentResult.value = 'failed'
    paymentError.value = error.message
  }
}

// 重新支付
const retryPayment = () => {
  showPaymentModal.value = false
  paymentResult.value = null
  paymentError.value = ''
}

// 页面加载时获取订单信息
onMounted(async () => {
  try {
    order.value = await ordersStore.fetchOrder(orderId)
    
    // 检查订单状态
    if (order.value.paymentStatus === 'PAID') {
      toast.add({
        title: '订单已支付',
        description: '该订单已完成支付',
        color: 'blue'
      })
      navigateTo(`/orders/${orderId}`)
      return
    }
    
    // 设置推荐支付方式
    selectedPaymentMethod.value = recommendedMethod.value
  } catch (error) {
    toast.add({
      title: '订单不存在',
      description: '找不到指定的订单',
      color: 'red'
    })
    navigateTo('/orders')
  }
})
</script>
