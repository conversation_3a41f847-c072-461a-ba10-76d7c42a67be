import { z } from 'zod'

/**
 * 更新用户个人信息
 * PUT /api/users/profile
 */

// 请求数据验证schema
const updateProfileSchema = z.object({
  nickname: z.string().min(1, '昵称不能为空').max(50, '昵称最多50个字符').optional(),
  bio: z.string().max(500, '个人简介最多500个字符').optional(),
  phone: z.string().regex(/^1[3-9]\d{9}$/, '手机号格式不正确').optional(),
  avatar: z.string().url('头像链接格式不正确').optional()
})

export default defineApiHandler(async (event) => {
  // 只允许PUT请求
  assertMethod(event, 'PUT')

  // 获取当前用户ID
  const userId = event.context.user?.id
  if (!userId) {
    throw new AuthenticationError('请先登录')
  }

  // 获取请求体数据
  const body = await readBody(event)

  // 验证请求数据
  const validatedData = updateProfileSchema.parse(body)

  // 检查手机号是否已被其他用户使用
  if (validatedData.phone) {
    const existingUser = await prisma.user.findFirst({
      where: {
        phone: validatedData.phone,
        id: { not: userId }
      }
    })

    if (existingUser) {
      throw new ConflictError('手机号已被其他用户使用')
    }
  }

  // 更新用户信息
  const updatedUser = await prisma.user.update({
    where: { id: userId },
    data: {
      ...validatedData,
      updatedAt: new Date()
    },
    select: {
      id: true,
      username: true,
      email: true,
      phone: true,
      avatar: true,
      nickname: true,
      bio: true,
      role: true,
      status: true,
      createdAt: true,
      updatedAt: true
    }
  })

  return createSuccessResponse(updatedUser, '个人信息更新成功')
})
