import type { Order, ShippingAddress, PaginatedResponse } from '~/types'

/**
 * 订单状态管理
 */
export const useOrdersStore = defineStore('orders', () => {
  // 状态
  const orders = ref<Order[]>([])
  const currentOrder = ref<Order | null>(null)
  const shippingAddresses = ref<ShippingAddress[]>([])
  const isLoading = ref(false)
  const pagination = ref({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  })

  // API实例
  const request = createAuthenticatedRequest()
  const authStore = useAuthStore()
  const toast = useToast()

  // 获取订单列表
  const fetchOrders = async (params: Record<string, any> = {}) => {
    if (!authStore.isLoggedIn) {
      throw new Error('请先登录')
    }

    try {
      isLoading.value = true
      
      const response = await request.get<PaginatedResponse<Order>>('/api/orders', {
        params: {
          page: pagination.value.page,
          pageSize: pagination.value.pageSize,
          ...params
        }
      })
      
      orders.value = response.items
      pagination.value = {
        page: response.page,
        pageSize: response.pageSize,
        total: response.total,
        totalPages: response.totalPages
      }
    } catch (error: any) {
      console.error('获取订单列表失败:', error)
      toast.add({
        title: '获取订单失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 获取订单详情
  const fetchOrder = async (orderId: number) => {
    if (!authStore.isLoggedIn) {
      throw new Error('请先登录')
    }

    try {
      isLoading.value = true
      
      const order = await request.get<Order>(`/api/orders/${orderId}`)
      currentOrder.value = order
      
      return order
    } catch (error: any) {
      console.error('获取订单详情失败:', error)
      toast.add({
        title: '获取订单详情失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 创建订单
  const createOrder = async (orderData: {
    items: Array<{ productId: number, quantity: number }>
    shippingAddressId: number
    remark?: string
  }) => {
    if (!authStore.isLoggedIn) {
      throw new Error('请先登录')
    }

    try {
      isLoading.value = true
      
      const order = await request.post<Order>('/api/orders', orderData)
      
      // 将新订单添加到列表开头
      orders.value.unshift(order)
      
      toast.add({
        title: '订单创建成功',
        description: `订单号：${order.orderNo}`,
        color: 'green'
      })
      
      return order
    } catch (error: any) {
      console.error('创建订单失败:', error)
      toast.add({
        title: '创建订单失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 取消订单
  const cancelOrder = async (orderId: number, reason?: string) => {
    if (!authStore.isLoggedIn) {
      throw new Error('请先登录')
    }

    try {
      await request.patch(`/api/orders/${orderId}/cancel`, { reason })
      
      // 更新本地订单状态
      const orderIndex = orders.value.findIndex(order => order.id === orderId)
      if (orderIndex !== -1) {
        orders.value[orderIndex].status = 'CANCELLED'
      }
      
      if (currentOrder.value?.id === orderId) {
        currentOrder.value.status = 'CANCELLED'
      }
      
      toast.add({
        title: '订单已取消',
        color: 'blue'
      })
    } catch (error: any) {
      console.error('取消订单失败:', error)
      toast.add({
        title: '取消订单失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 确认收货
  const confirmOrder = async (orderId: number) => {
    if (!authStore.isLoggedIn) {
      throw new Error('请先登录')
    }

    try {
      await request.patch(`/api/orders/${orderId}/confirm`)
      
      // 更新本地订单状态
      const orderIndex = orders.value.findIndex(order => order.id === orderId)
      if (orderIndex !== -1) {
        orders.value[orderIndex].status = 'DELIVERED'
      }
      
      if (currentOrder.value?.id === orderId) {
        currentOrder.value.status = 'DELIVERED'
      }
      
      toast.add({
        title: '确认收货成功',
        color: 'green'
      })
    } catch (error: any) {
      console.error('确认收货失败:', error)
      toast.add({
        title: '确认收货失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 申请退款
  const requestRefund = async (orderId: number, reason: string) => {
    if (!authStore.isLoggedIn) {
      throw new Error('请先登录')
    }

    try {
      await request.post(`/api/orders/${orderId}/refund`, { reason })
      
      toast.add({
        title: '退款申请已提交',
        description: '我们会尽快处理您的申请',
        color: 'blue'
      })
    } catch (error: any) {
      console.error('申请退款失败:', error)
      toast.add({
        title: '申请退款失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 获取收货地址列表
  const fetchShippingAddresses = async () => {
    if (!authStore.isLoggedIn) {
      throw new Error('请先登录')
    }

    try {
      const addresses = await request.get<ShippingAddress[]>('/api/users/addresses')
      shippingAddresses.value = addresses
      return addresses
    } catch (error: any) {
      console.error('获取收货地址失败:', error)
      toast.add({
        title: '获取收货地址失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 添加收货地址
  const addShippingAddress = async (addressData: Omit<ShippingAddress, 'id' | 'userId' | 'createdAt' | 'updatedAt'>) => {
    if (!authStore.isLoggedIn) {
      throw new Error('请先登录')
    }

    try {
      const address = await request.post<ShippingAddress>('/api/users/addresses', addressData)
      shippingAddresses.value.push(address)
      
      toast.add({
        title: '地址添加成功',
        color: 'green'
      })
      
      return address
    } catch (error: any) {
      console.error('添加收货地址失败:', error)
      toast.add({
        title: '添加地址失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 更新收货地址
  const updateShippingAddress = async (addressId: number, addressData: Partial<ShippingAddress>) => {
    if (!authStore.isLoggedIn) {
      throw new Error('请先登录')
    }

    try {
      const address = await request.put<ShippingAddress>(`/api/users/addresses/${addressId}`, addressData)
      
      const index = shippingAddresses.value.findIndex(addr => addr.id === addressId)
      if (index !== -1) {
        shippingAddresses.value[index] = address
      }
      
      toast.add({
        title: '地址更新成功',
        color: 'green'
      })
      
      return address
    } catch (error: any) {
      console.error('更新收货地址失败:', error)
      toast.add({
        title: '更新地址失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 删除收货地址
  const deleteShippingAddress = async (addressId: number) => {
    if (!authStore.isLoggedIn) {
      throw new Error('请先登录')
    }

    try {
      await request.delete(`/api/users/addresses/${addressId}`)
      
      const index = shippingAddresses.value.findIndex(addr => addr.id === addressId)
      if (index !== -1) {
        shippingAddresses.value.splice(index, 1)
      }
      
      toast.add({
        title: '地址删除成功',
        color: 'blue'
      })
    } catch (error: any) {
      console.error('删除收货地址失败:', error)
      toast.add({
        title: '删除地址失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 设置默认地址
  const setDefaultAddress = async (addressId: number) => {
    if (!authStore.isLoggedIn) {
      throw new Error('请先登录')
    }

    try {
      await request.patch(`/api/users/addresses/${addressId}/default`)
      
      // 更新本地状态
      shippingAddresses.value.forEach(addr => {
        addr.isDefault = addr.id === addressId
      })
      
      toast.add({
        title: '默认地址设置成功',
        color: 'green'
      })
    } catch (error: any) {
      console.error('设置默认地址失败:', error)
      toast.add({
        title: '设置失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 计算属性
  const defaultAddress = computed(() => 
    shippingAddresses.value.find(addr => addr.isDefault)
  )

  const getOrdersByStatus = (status: string) => 
    orders.value.filter(order => order.status === status)

  // 清空状态
  const clearState = () => {
    orders.value = []
    currentOrder.value = null
    shippingAddresses.value = []
    pagination.value = {
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    }
  }

  return {
    // 状态
    orders: readonly(orders),
    currentOrder: readonly(currentOrder),
    shippingAddresses: readonly(shippingAddresses),
    isLoading: readonly(isLoading),
    pagination: readonly(pagination),
    
    // 方法
    fetchOrders,
    fetchOrder,
    createOrder,
    cancelOrder,
    confirmOrder,
    requestRefund,
    fetchShippingAddresses,
    addShippingAddress,
    updateShippingAddress,
    deleteShippingAddress,
    setDefaultAddress,
    
    // 计算属性
    defaultAddress,
    getOrdersByStatus,
    
    // 工具方法
    clearState
  }
})
