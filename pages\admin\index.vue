<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">管理员控制台</h1>
        <p class="text-gray-600 mt-2">系统管理和数据监控</p>
      </div>
      
      <div class="flex items-center space-x-4">
        <UButton variant="outline" @click="adminStore.exportData('users')">
          <Icon name="heroicons:arrow-down-tray" class="w-4 h-4 mr-2" />
          导出数据
        </UButton>
        <UButton @click="showNotificationModal = true">
          <Icon name="heroicons:megaphone" class="w-4 h-4 mr-2" />
          发送通知
        </UButton>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <UCard class="p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">总用户数</p>
            <p class="text-2xl font-bold text-gray-900">{{ adminStore.statistics.totalUsers }}</p>
            <p class="text-xs text-green-600 mt-1">今日新增 {{ adminStore.statistics.todayUsers }}</p>
          </div>
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <Icon name="heroicons:users" class="w-6 h-6 text-blue-600" />
          </div>
        </div>
      </UCard>

      <UCard class="p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">总商品数</p>
            <p class="text-2xl font-bold text-gray-900">{{ adminStore.statistics.totalProducts }}</p>
            <p class="text-xs text-yellow-600 mt-1">待审核 {{ adminStore.pendingProducts.length }}</p>
          </div>
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <Icon name="heroicons:shopping-bag" class="w-6 h-6 text-green-600" />
          </div>
        </div>
      </UCard>

      <UCard class="p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">总订单数</p>
            <p class="text-2xl font-bold text-gray-900">{{ adminStore.statistics.totalOrders }}</p>
            <p class="text-xs text-blue-600 mt-1">今日 {{ adminStore.statistics.todayOrders }}</p>
          </div>
          <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
            <Icon name="heroicons:document-text" class="w-6 h-6 text-yellow-600" />
          </div>
        </div>
      </UCard>

      <UCard class="p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">总收入</p>
            <p class="text-2xl font-bold text-gray-900">¥{{ adminStore.statistics.totalRevenue.toLocaleString() }}</p>
            <p class="text-xs text-purple-600 mt-1">今日 ¥{{ adminStore.statistics.todayRevenue.toLocaleString() }}</p>
          </div>
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
            <Icon name="heroicons:currency-dollar" class="w-6 h-6 text-purple-600" />
          </div>
        </div>
      </UCard>
    </div>

    <!-- 待处理事项 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
      <!-- 待审核商品 -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold">待审核商品</h3>
            <UButton variant="ghost" size="sm" @click="navigateTo('/admin/products?status=PENDING')">
              查看全部
            </UButton>
          </div>
        </template>
        
        <div class="space-y-4">
          <div 
            v-for="product in adminStore.pendingProducts.slice(0, 5)"
            :key="product.id"
            class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"
          >
            <img 
              :src="product.images[0] || '/images/placeholder.jpg'"
              :alt="product.name"
              class="w-10 h-10 object-cover rounded"
            >
            <div class="flex-1 min-w-0">
              <div class="font-medium truncate">{{ product.name }}</div>
              <div class="text-sm text-gray-500">¥{{ product.price }}</div>
            </div>
            <div class="flex space-x-1">
              <UButton size="xs" @click="reviewProduct(product.id, 'APPROVE')">
                通过
              </UButton>
              <UButton size="xs" variant="outline" color="red" @click="reviewProduct(product.id, 'REJECT')">
                拒绝
              </UButton>
            </div>
          </div>
          
          <div v-if="adminStore.pendingProducts.length === 0" class="text-center py-4 text-gray-500">
            暂无待审核商品
          </div>
        </div>
      </UCard>

      <!-- 退款申请 -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold">退款申请</h3>
            <UButton variant="ghost" size="sm" @click="navigateTo('/admin/orders?refund=PENDING')">
              查看全部
            </UButton>
          </div>
        </template>
        
        <div class="space-y-4">
          <div 
            v-for="order in adminStore.refundOrders.slice(0, 5)"
            :key="order.id"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          >
            <div>
              <div class="font-medium">订单 #{{ order.orderNo }}</div>
              <div class="text-sm text-gray-500">¥{{ order.paymentAmount }}</div>
            </div>
            <div class="flex space-x-1">
              <UButton size="xs" @click="processRefund(order.id, 'APPROVE')">
                批准
              </UButton>
              <UButton size="xs" variant="outline" color="red" @click="processRefund(order.id, 'REJECT')">
                拒绝
              </UButton>
            </div>
          </div>
          
          <div v-if="adminStore.refundOrders.length === 0" class="text-center py-4 text-gray-500">
            暂无退款申请
          </div>
        </div>
      </UCard>

      <!-- 系统状态 -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">系统状态</h3>
        </template>
        
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">活跃用户</span>
            <span class="font-medium">{{ adminStore.statistics.activeUsers }}</span>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">待处理订单</span>
            <span class="font-medium">{{ adminStore.statistics.pendingOrders }}</span>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">举报内容</span>
            <span class="font-medium">{{ adminStore.statistics.reportedPosts }}</span>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">封禁用户</span>
            <span class="font-medium text-red-600">{{ adminStore.bannedUsers.length }}</span>
          </div>
        </div>
      </UCard>
    </div>

    <!-- 快捷导航 -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
      <UButton
        variant="outline"
        size="lg"
        block
        @click="navigateTo('/admin/users')"
        class="h-20 flex-col"
      >
        <Icon name="heroicons:users" class="w-6 h-6 mb-2" />
        用户管理
      </UButton>

      <UButton
        variant="outline"
        size="lg"
        block
        @click="navigateTo('/admin/products')"
        class="h-20 flex-col"
      >
        <Icon name="heroicons:shopping-bag" class="w-6 h-6 mb-2" />
        商品管理
      </UButton>

      <UButton
        variant="outline"
        size="lg"
        block
        @click="navigateTo('/admin/orders')"
        class="h-20 flex-col"
      >
        <Icon name="heroicons:document-text" class="w-6 h-6 mb-2" />
        订单管理
      </UButton>

      <UButton
        variant="outline"
        size="lg"
        block
        @click="navigateTo('/admin/analytics')"
        class="h-20 flex-col"
      >
        <Icon name="heroicons:chart-bar" class="w-6 h-6 mb-2" />
        数据分析
      </UButton>
    </div>

    <!-- 系统通知模态框 -->
    <UModal v-model="showNotificationModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">发送系统通知</h3>
        </template>
        
        <div class="space-y-4">
          <UInput
            v-model="notificationForm.title"
            placeholder="通知标题"
            label="标题"
          />
          
          <UTextarea
            v-model="notificationForm.content"
            placeholder="通知内容"
            label="内容"
            rows="4"
          />
          
          <USelect
            v-model="notificationForm.type"
            :options="notificationTypes"
            placeholder="选择通知类型"
            label="类型"
          />
          
          <UInput
            v-model="notificationForm.actionUrl"
            placeholder="跳转链接（可选）"
            label="跳转链接"
          />
          
          <div class="flex justify-end space-x-3">
            <UButton variant="ghost" @click="showNotificationModal = false">
              取消
            </UButton>
            <UButton @click="sendNotification" :loading="isSendingNotification">
              发送通知
            </UButton>
          </div>
        </div>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
// 页面元信息
definePageMeta({
  middleware: ['auth', 'admin']
})

// 页面SEO
useHead({
  title: '管理员控制台 - 社交购物网站'
})

// 状态管理
const adminStore = useAdminStore()
const toast = useToast()

// 响应式数据
const showNotificationModal = ref(false)
const isSendingNotification = ref(false)

// 通知表单
const notificationForm = reactive({
  title: '',
  content: '',
  type: 'SYSTEM',
  actionUrl: ''
})

// 通知类型选项
const notificationTypes = [
  { value: 'SYSTEM', label: '系统通知' },
  { value: 'PROMOTION', label: '促销活动' },
  { value: 'MAINTENANCE', label: '维护公告' },
  { value: 'SECURITY', label: '安全提醒' }
]

// 审核商品
const reviewProduct = async (productId: number, action: 'APPROVE' | 'REJECT') => {
  const reason = action === 'REJECT' ? prompt('请输入拒绝原因：') : undefined
  if (action === 'REJECT' && !reason) return
  
  try {
    await adminStore.reviewProduct(productId, action, reason)
    // 刷新统计数据
    await adminStore.fetchStatistics()
  } catch (error) {
    console.error('审核商品失败:', error)
  }
}

// 处理退款
const processRefund = async (orderId: number, action: 'APPROVE' | 'REJECT') => {
  const reason = action === 'REJECT' ? prompt('请输入拒绝原因：') : undefined
  if (action === 'REJECT' && !reason) return
  
  try {
    await adminStore.processRefund(orderId, action, reason)
    // 刷新统计数据
    await adminStore.fetchStatistics()
  } catch (error) {
    console.error('处理退款失败:', error)
  }
}

// 发送系统通知
const sendNotification = async () => {
  if (!notificationForm.title.trim() || !notificationForm.content.trim()) {
    toast.add({
      title: '请填写完整信息',
      color: 'yellow'
    })
    return
  }
  
  try {
    isSendingNotification.value = true
    
    await adminStore.sendSystemNotification({
      title: notificationForm.title,
      content: notificationForm.content,
      type: notificationForm.type,
      actionUrl: notificationForm.actionUrl || undefined
    })
    
    // 重置表单
    Object.assign(notificationForm, {
      title: '',
      content: '',
      type: 'SYSTEM',
      actionUrl: ''
    })
    
    showNotificationModal.value = false
  } catch (error) {
    console.error('发送通知失败:', error)
  } finally {
    isSendingNotification.value = false
  }
}

// 页面加载时获取数据
onMounted(async () => {
  try {
    await Promise.all([
      adminStore.fetchStatistics(),
      adminStore.fetchAllProducts({ status: 'PENDING', pageSize: 5 }),
      adminStore.fetchAllOrders({ refundStatus: 'PENDING', pageSize: 5 })
    ])
  } catch (error) {
    console.error('加载数据失败:', error)
  }
})
</script>
