<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- 头部 -->
      <div class="text-center">
        <NuxtLink to="/" class="flex items-center justify-center space-x-2 mb-6">
          <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-lg">S</span>
          </div>
          <span class="text-2xl font-bold text-gray-900">社交购物</span>
        </NuxtLink>
        <h2 class="text-3xl font-bold text-gray-900">
          登录您的账户
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          还没有账户？
          <NuxtLink 
            to="/register" 
            class="font-medium text-primary-600 hover:text-primary-500 transition-colors"
          >
            立即注册
          </NuxtLink>
        </p>
      </div>

      <!-- 登录表单 -->
      <UCard class="p-6">
        <UForm 
          ref="loginForm"
          :schema="loginSchema" 
          :state="formData" 
          @submit="handleLogin"
          class="space-y-6"
        >
          <!-- 用户名/邮箱 -->
          <UFormGroup 
            label="用户名或邮箱" 
            name="username"
            required
          >
            <UInput
              v-model="formData.username"
              placeholder="请输入用户名或邮箱"
              size="lg"
              :disabled="authStore.isLoading"
              autocomplete="username"
            />
          </UFormGroup>

          <!-- 密码 -->
          <UFormGroup 
            label="密码" 
            name="password"
            required
          >
            <UInput
              v-model="formData.password"
              type="password"
              placeholder="请输入密码"
              size="lg"
              :disabled="authStore.isLoading"
              autocomplete="current-password"
            />
          </UFormGroup>

          <!-- 记住我和忘记密码 -->
          <div class="flex items-center justify-between">
            <UCheckbox
              v-model="formData.rememberMe"
              label="记住我"
              :disabled="authStore.isLoading"
            />
            <NuxtLink 
              to="/forgot-password" 
              class="text-sm text-primary-600 hover:text-primary-500 transition-colors"
            >
              忘记密码？
            </NuxtLink>
          </div>

          <!-- 登录按钮 -->
          <UButton
            type="submit"
            size="lg"
            block
            :loading="authStore.isLoading"
            :disabled="!isFormValid"
          >
            {{ authStore.isLoading ? '登录中...' : '登录' }}
          </UButton>
        </UForm>

        <!-- 分割线 -->
        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300" />
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white text-gray-500">或者</span>
            </div>
          </div>
        </div>

        <!-- 第三方登录 -->
        <div class="mt-6 space-y-3">
          <UButton
            variant="outline"
            size="lg"
            block
            :disabled="authStore.isLoading"
            @click="handleThirdPartyLogin('wechat')"
          >
            <Icon name="simple-icons:wechat" class="w-5 h-5 mr-2" />
            微信登录
          </UButton>
          
          <UButton
            variant="outline"
            size="lg"
            block
            :disabled="authStore.isLoading"
            @click="handleThirdPartyLogin('qq')"
          >
            <Icon name="simple-icons:qq" class="w-5 h-5 mr-2" />
            QQ登录
          </UButton>
        </div>
      </UCard>

      <!-- 底部链接 -->
      <div class="text-center text-sm text-gray-600">
        <p>
          登录即表示您同意我们的
          <NuxtLink to="/terms" class="text-primary-600 hover:text-primary-500">
            服务条款
          </NuxtLink>
          和
          <NuxtLink to="/privacy" class="text-primary-600 hover:text-primary-500">
            隐私政策
          </NuxtLink>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod'
import type { LoginForm } from '~/types'

// 页面元信息
definePageMeta({
  layout: false,
  middleware: 'guest'
})

// 页面SEO
useHead({
  title: '用户登录 - 社交购物网站',
  meta: [
    { name: 'description', content: '登录您的社交购物账户，享受个性化购物体验' }
  ]
})

// 状态管理
const authStore = useAuthStore()
const toast = useToast()

// 表单数据
const formData = reactive<LoginForm>({
  username: '',
  password: '',
  rememberMe: false
})

// 表单验证规则
const loginSchema = z.object({
  username: z.string().min(1, '请输入用户名或邮箱'),
  password: z.string().min(1, '请输入密码'),
  rememberMe: z.boolean().optional()
})

// 表单引用
const loginForm = ref()

// 计算属性
const isFormValid = computed(() => {
  return formData.username.trim() && formData.password.trim()
})

// 处理登录
const handleLogin = async () => {
  try {
    await authStore.login(formData)
    
    // 获取重定向地址
    const route = useRoute()
    const redirectTo = route.query.redirect as string || '/'
    
    // 登录成功后跳转
    await navigateTo(redirectTo)
  } catch (error) {
    // 错误已在store中处理
    console.error('登录失败:', error)
  }
}

// 第三方登录
const handleThirdPartyLogin = (provider: string) => {
  toast.add({
    title: '功能开发中',
    description: `${provider}登录功能正在开发中，敬请期待！`,
    color: 'yellow'
  })
}

// 页面加载时的处理
onMounted(() => {
  // 如果已经登录，跳转到首页
  if (authStore.isLoggedIn) {
    navigateTo('/')
  }
  
  // 自动聚焦到用户名输入框
  nextTick(() => {
    const usernameInput = document.querySelector('input[name="username"]') as HTMLInputElement
    if (usernameInput) {
      usernameInput.focus()
    }
  })
})
</script>

<style scoped>
/* 自定义样式 */
.login-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
</style>
