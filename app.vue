<template>
  <div>
    <!-- 全局布局 -->
    <NuxtLayout>
      <!-- 页面内容 -->
      <NuxtPage />
    </NuxtLayout>
    
    <!-- 全局通知组件 -->
    <UNotifications />
    
    <!-- 全局模态框 -->
    <UModals />
  </div>
</template>

<script setup lang="ts">
// 设置页面元信息
useHead({
  title: '社交购物网站',
  meta: [
    { name: 'description', content: '集购物与社交于一体的现代化电商平台' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' }
  ],
  link: [
    { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
  ]
})

// 全局错误处理
onErrorCaptured((error) => {
  console.error('Global error:', error)
  return false
})
</script>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', '<PERSON>o <PERSON>s SC', sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8fafc;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
