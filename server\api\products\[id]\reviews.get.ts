/**
 * 获取商品评价列表
 * GET /api/products/:id/reviews
 */

export default defineApiHandler(async (event) => {
  // 获取商品ID
  const productId = parseInt(getRouterParam(event, 'id') || '0')
  if (!productId || isNaN(productId)) {
    throw new ValidationError('商品ID格式不正确')
  }

  const query = getQuery(event)
  
  // 解析分页参数
  const { page, pageSize, skip, take } = parsePaginationQuery(query)
  
  // 解析排序参数
  const allowedSortFields = ['createdAt', 'rating', 'helpful']
  const orderBy = parseSortQuery(query, allowedSortFields)
  
  // 构建查询条件
  const where: any = {
    productId,
    status: 'PUBLISHED'
  }
  
  // 评分过滤
  if (query.rating) {
    where.rating = parseInt(query.rating as string)
  }
  
  // 是否有图片过滤
  if (query.hasImages === 'true') {
    where.images = {
      not: {
        equals: []
      }
    }
  }
  
  // 查询评价列表和总数
  const [reviews, total] = await Promise.all([
    prisma.review.findMany({
      where,
      orderBy,
      skip,
      take,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            nickname: true,
            avatar: true
          }
        },
        _count: {
          select: {
            helpful: true
          }
        }
      }
    }),
    prisma.review.count({ where })
  ])

  // 获取当前用户对评价的点赞状态
  const currentUserId = event.context.user?.id
  let userHelpfulMap: Record<number, boolean> = {}
  
  if (currentUserId && reviews.length > 0) {
    const userHelpful = await prisma.reviewHelpful.findMany({
      where: {
        userId: currentUserId,
        reviewId: {
          in: reviews.map(r => r.id)
        }
      },
      select: {
        reviewId: true
      }
    })
    
    userHelpfulMap = userHelpful.reduce((acc, item) => {
      acc[item.reviewId] = true
      return acc
    }, {} as Record<number, boolean>)
  }

  // 格式化评价数据
  const formattedReviews = reviews.map(review => ({
    id: review.id,
    rating: review.rating,
    content: review.content,
    images: review.images,
    anonymous: review.anonymous,
    helpfulCount: review._count.helpful,
    isHelpful: userHelpfulMap[review.id] || false,
    createdAt: review.createdAt,
    updatedAt: review.updatedAt,
    user: review.anonymous ? null : review.user
  }))

  // 计算总页数
  const totalPages = Math.ceil(total / pageSize)

  const result = {
    items: formattedReviews,
    total,
    page,
    pageSize,
    totalPages
  }

  return createSuccessResponse(result, '获取评价列表成功')
})
