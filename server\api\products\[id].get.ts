/**
 * 获取商品详情
 * GET /api/products/:id
 */

export default defineApiHandler(async event => {
  // 获取商品ID参数
  const productId = parseInt(getRouterParam(event, 'id') || '0')
  if (!productId || isNaN(productId)) {
    throw new ValidationError('商品ID格式不正确')
  }

  // 查询商品详情
  const product = await prisma.product.findUnique({
    where: { id: productId },
    include: {
      category: {
        select: {
          id: true,
          name: true,
          slug: true,
          parent: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          }
        }
      },
      merchant: {
        select: {
          id: true,
          username: true,
          nickname: true,
          avatar: true,
          role: true,
          _count: {
            select: {
              products: {
                where: { status: 'ACTIVE' }
              },
              followers: true
            }
          }
        }
      },
      reviews: {
        take: 5,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              nickname: true,
              avatar: true
            }
          }
        }
      },
      _count: {
        select: {
          reviews: true,
          favorites: true,
          cartItems: true
        }
      }
    }
  })

  if (!product) {
    throw new NotFoundError('商品不存在')
  }

  // 检查商品状态
  if (product.status !== 'ACTIVE') {
    throw new NotFoundError('商品不存在或已下架')
  }

  // 检查是否已收藏（如果用户已登录）
  let isFavorited = false
  let isInCart = false
  const currentUserId = event.context.user?.id

  if (currentUserId) {
    const [favorite, cartItem] = await Promise.all([
      prisma.favorite.findUnique({
        where: {
          userId_productId: {
            userId: currentUserId,
            productId: productId
          }
        }
      }),
      prisma.cartItem.findUnique({
        where: {
          userId_productId: {
            userId: currentUserId,
            productId: productId
          }
        }
      })
    ])

    isFavorited = !!favorite
    isInCart = !!cartItem
  }

  // 获取相关商品推荐
  const relatedProducts = await prisma.product.findMany({
    where: {
      categoryId: product.categoryId,
      id: { not: productId },
      status: 'ACTIVE'
    },
    take: 4,
    orderBy: { sales: 'desc' },
    select: {
      id: true,
      name: true,
      price: true,
      originalPrice: true,
      images: true,
      rating: true,
      sales: true
    }
  })

  // 格式化返回数据
  const productDetail = {
    ...product,
    reviewCount: product._count.reviews,
    favoriteCount: product._count.favorites,
    cartCount: product._count.cartItems,
    isFavorited,
    isInCart,
    merchant: {
      ...product.merchant,
      productsCount: product.merchant._count.products,
      followersCount: product.merchant._count.followers,
      _count: undefined
    },
    relatedProducts,
    _count: undefined
  }

  // 记录商品查看日志
  await logProductView(productId, event, event.context.user?.id)

  return createSuccessResponse(productDetail, '获取商品详情成功')
})
