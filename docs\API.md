# API接口文档

本文档描述了社交购物网站的API接口规范。

## 📋 基础信息

- **Base URL**: `/api`
- **Content-Type**: `application/json`
- **认证方式**: <PERSON><PERSON>ken (JWT)

## 🔐 认证说明

需要认证的接口需要在请求头中包含JWT token：

```http
Authorization: Bearer <your-jwt-token>
```

## 📊 响应格式

### 成功响应

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 响应数据
  }
}
```

### 错误响应

```json
{
  "success": false,
  "message": "错误信息",
  "errors": [
    // 详细错误信息（可选）
  ]
}
```

### 分页响应

```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "items": [],
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10
  }
}
```

## 🔑 认证接口

### 用户注册

```http
POST /api/auth/register
```

**请求参数:**

```json
{
  "username": "string", // 用户名 (3-20字符)
  "email": "string", // 邮箱
  "password": "string", // 密码 (6-50字符)
  "phone": "string", // 手机号 (可选)
  "captcha": "string" // 验证码 (4-6位)
}
```

**响应数据:**

```json
{
  "user": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "USER"
  },
  "token": "jwt-token"
}
```

### 用户登录

```http
POST /api/auth/login
```

**请求参数:**

```json
{
  "username": "string", // 用户名或邮箱
  "password": "string", // 密码
  "rememberMe": "boolean", // 记住我 (可选)
  "captcha": "string" // 验证码 (可选)
}
```

**响应数据:**

```json
{
  "user": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "role": "USER"
  },
  "token": "jwt-token",
  "expiresIn": "7d"
}
```

### 用户登出

```http
POST /api/auth/logout
```

**需要认证**: ✅

**请求参数:** 无

**响应数据:**

```json
{
  "success": true,
  "message": "登出成功",
  "data": null
}
```

**说明:**

- 调用此接口会记录用户登出日志
- 客户端应在调用后清除本地存储的token和用户信息
- 即使接口调用失败，客户端也应清除本地认证信息

### 检查用户名是否可用

```http
GET /api/auth/check-username?username=testuser
```

**查询参数:**

- `username`: 要检查的用户名

**响应数据:**

```json
{
  "available": true,
  "reason": null,
  "message": "用户名可用"
}
```

**可能的响应状态:**

- `available: true` - 用户名可用
- `available: false, reason: "exists"` - 用户名已被使用
- `available: false, reason: "format"` - 用户名格式不正确

**用户名规则:**

- 长度：3-20个字符
- 字符：只能包含字母、数字和下划线
- 不能以数字开头
- 不能使用系统保留词

### 检查邮箱是否可用

```http
GET /api/auth/check-email?email=<EMAIL>
```

**查询参数:**

- `email`: 要检查的邮箱地址

**响应数据:**

```json
{
  "available": true,
  "reason": null,
  "message": "邮箱可用"
}
```

**可能的响应状态:**

- `available: true` - 邮箱可用
- `available: false, reason: "exists"` - 邮箱已被使用
- `available: false, reason: "format"` - 邮箱格式不正确

**邮箱规则:**

- 必须是有效的邮箱格式
- 不支持临时邮箱域名
- 长度不超过254个字符

## 👤 用户接口

### 获取个人信息

```http
GET /api/users/profile
```

**需要认证**: ✅

### 更新个人信息

```http
PUT /api/users/profile
```

**需要认证**: ✅

**请求参数:**

```json
{
  "nickname": "string", // 昵称 (可选)
  "bio": "string", // 个人简介 (可选)
  "phone": "string", // 手机号 (可选)
  "avatar": "string" // 头像链接 (可选)
}
```

### 获取用户信息

```http
GET /api/users/:id
```

**路径参数:**

- `id`: 用户ID

### 获取用户订单列表

```http
GET /api/users/orders
```

**需要认证**: ✅

**查询参数:**

- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 10)
- `status`: 订单状态 (PENDING/PAID/SHIPPED/COMPLETED/CANCELLED)
- `startDate`: 开始日期
- `endDate`: 结束日期
- `sortBy`: 排序字段 (createdAt/totalAmount/status)
- `sortOrder`: 排序方向 (asc/desc)

### 获取用户收藏列表

```http
GET /api/users/favorites
```

**需要认证**: ✅

**查询参数:**

- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 10)
- `categoryId`: 分类ID
- `minPrice`: 最低价格
- `maxPrice`: 最高价格
- `sortBy`: 排序字段 (createdAt/product.price/product.sales)
- `sortOrder`: 排序方向 (asc/desc)

## 🛍️ 商品接口

### 获取商品列表

```http
GET /api/products
```

**查询参数:**

- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 10, 最大: 100)
- `keyword`: 搜索关键词
- `filter_categoryId`: 分类ID
- `filter_minPrice`: 最低价格
- `filter_maxPrice`: 最高价格
- `filter_merchantId`: 商家ID
- `filter_inStock`: 是否有库存 (true/false)
- `sortBy`: 排序字段 (price/sales/rating/createdAt)
- `sortOrder`: 排序方向 (asc/desc)

### 获取商品详情

```http
GET /api/products/:id
```

**路径参数:**

- `id`: 商品ID

### 商品搜索

```http
GET /api/products/search
```

**查询参数:**

- `q`: 搜索关键词 (必填)
- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 10, 最大: 100)
- `categoryId`: 分类ID
- `minPrice`: 最低价格
- `maxPrice`: 最高价格
- `merchantId`: 商家ID
- `inStock`: 是否有库存 (true/false)
- `sortBy`: 排序字段 (createdAt/price/sales/rating)
- `sortOrder`: 排序方向 (asc/desc)

**响应数据:**

```json
{
  "items": [
    {
      "id": 1,
      "name": "商品名称",
      "description": "商品描述",
      "price": 99.99,
      "originalPrice": 129.99,
      "images": ["image1.jpg", "image2.jpg"],
      "stock": 100,
      "sales": 50,
      "rating": 4.5,
      "reviewsCount": 20,
      "favoritesCount": 10,
      "tags": ["热销", "新品"],
      "category": {
        "id": 1,
        "name": "分类名称",
        "slug": "category-slug"
      },
      "merchant": {
        "id": 1,
        "username": "merchant",
        "nickname": "商家名称",
        "avatar": "avatar.jpg"
      }
    }
  ],
  "total": 100,
  "page": 1,
  "pageSize": 10,
  "totalPages": 10,
  "keyword": "搜索关键词",
  "filters": {
    "categoryId": 1,
    "minPrice": 50,
    "maxPrice": 200,
    "inStock": true
  }
}
```

**搜索规则:**

- 支持商品名称、描述、标签的模糊搜索
- 搜索不区分大小写
- 支持多种筛选和排序条件
- 会记录搜索日志用于分析

### 获取搜索建议

```http
GET /api/search/suggestions
```

**查询参数:**

- `q`: 搜索关键词 (可选，用于自动补全)
- `limit`: 返回数量限制 (默认: 10, 最大: 20)

**响应数据:**

```json
{
  "suggestions": ["手机", "电脑", "耳机", "运动鞋", "化妆品"],
  "keyword": "手机",
  "count": 5
}
```

**说明:**

- 如果提供关键词，返回相关的自动补全建议
- 如果不提供关键词，返回热门搜索关键词
- 建议基于搜索历史和商品数据生成

### 获取商品评价列表

```http
GET /api/products/:id/reviews
```

**路径参数:**

- `id`: 商品ID

**查询参数:**

- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 10)
- `rating`: 评分过滤 (1-5)
- `hasImages`: 是否有图片 (true/false)
- `sortBy`: 排序字段 (createdAt/rating/helpful)
- `sortOrder`: 排序方向 (asc/desc)

**响应数据:**

```json
{
  "items": [
    {
      "id": 1,
      "rating": 5,
      "content": "商品质量很好，推荐购买！",
      "images": ["image1.jpg", "image2.jpg"],
      "anonymous": false,
      "helpfulCount": 10,
      "isHelpful": false,
      "createdAt": "2024-01-15T10:00:00Z",
      "user": {
        "id": 1,
        "username": "testuser",
        "nickname": "测试用户",
        "avatar": "avatar.jpg"
      }
    }
  ],
  "total": 100,
  "page": 1,
  "pageSize": 10,
  "totalPages": 10
}
```

### 提交商品评价

```http
POST /api/products/:id/reviews
```

**需要认证**: ✅

**路径参数:**

- `id`: 商品ID

**请求参数:**

```json
{
  "rating": 5,
  "content": "商品质量很好，推荐购买！",
  "images": ["image1.jpg", "image2.jpg"],
  "anonymous": false
}
```

**说明:**

- 只有购买过该商品的用户才能评价
- 每个用户对同一商品只能评价一次
- 评价提交后会更新商品的评分统计

## 🎫 优惠券接口

### 获取商品可用优惠券

```http
GET /api/products/:id/coupons
```

**路径参数:**

- `id`: 商品ID

**响应数据:**

```json
{
  "productId": 1,
  "productName": "商品名称",
  "coupons": {
    "shared": [
      {
        "id": 1,
        "name": "满100减10",
        "description": "全场通用优惠券",
        "type": "SHARED",
        "discountType": "FIXED",
        "discountValue": 10,
        "minOrderAmount": 100,
        "isReceived": false,
        "canUse": true,
        "remainingCount": 1000
      }
    ],
    "exclusive": [
      {
        "id": 2,
        "name": "8折优惠券",
        "type": "EXCLUSIVE",
        "discountType": "PERCENTAGE",
        "discountValue": 20,
        "minOrderAmount": 200,
        "maxDiscountAmount": 50,
        "isReceived": true,
        "canUse": true
      }
    ],
    "total": 2
  }
}
```

### 领取优惠券

```http
POST /api/coupons/:id/claim
```

**需要认证**: ✅

**路径参数:**

- `id`: 优惠券ID

**说明:**

- 每个用户只能领取一次同一张优惠券
- 需要检查优惠券的有效期和库存
- 有每日领取数量限制

### 获取用户可用优惠券

```http
GET /api/users/coupons
```

**需要认证**: ✅

**查询参数:**

- `status`: 优惠券状态 (UNUSED/USED/EXPIRED)
- `orderAmount`: 订单金额 (用于计算优惠)
- `productIds`: 商品ID列表 (逗号分隔)
- `categoryIds`: 分类ID列表 (逗号分隔)
- `merchantIds`: 商家ID列表 (逗号分隔)

### 计算优惠券优惠金额

```http
POST /api/coupons/calculate
```

**需要认证**: ✅

**请求参数:**

```json
{
  "userCouponIds": [1, 2, 3],
  "orderAmount": 299.99,
  "items": [
    {
      "productId": 1,
      "quantity": 2,
      "price": 99.99
    }
  ]
}
```

**优惠券使用规则:**

- 同享券可以多张同时使用
- 互斥券只能使用一张，且不能与同享券同时使用
- 需要满足最低订单金额要求
- 需要检查适用范围（商品、分类、商家）

## 📁 文件上传接口

### 图片上传

```http
POST /api/upload/images
```

**需要认证**: ✅

**请求格式**: `multipart/form-data`

**请求参数:**

- `files`: 图片文件（支持多文件上传）

**支持的格式**: JPEG, JPG, PNG, GIF, WebP
**文件大小限制**: 5MB

**响应数据:**

```json
{
  "success": true,
  "message": "图片上传成功",
  "data": {
    "files": ["/uploads/images/uuid-filename.jpg", "/uploads/images/uuid-filename2.png"],
    "count": 2
  }
}
```

### 头像上传

```http
POST /api/upload/avatar
```

**需要认证**: ✅

**请求格式**: `multipart/form-data`

**请求参数:**

- `files`: 头像文件（单文件）

**支持的格式**: JPEG, JPG, PNG
**文件大小限制**: 2MB

**响应数据:**

```json
{
  "success": true,
  "message": "头像上传成功",
  "data": {
    "avatar": "/uploads/avatars/avatar_uuid.jpg"
  }
}
```

**说明:**

- 上传成功后会自动更新用户头像
- 文件名会自动生成UUID避免冲突
- 支持的图片格式会自动验证
- 超过大小限制会返回错误

## 📂 分类接口

### 获取分类列表

```http
GET /api/categories
```

**查询参数:**

- `topLevel`: 是否只获取顶级分类 (true/false)

## 🛒 购物车接口

### 获取购物车

```http
GET /api/cart
```

**需要认证**: ✅

### 添加商品到购物车

```http
POST /api/cart
```

**需要认证**: ✅

**请求参数:**

```json
{
  "productId": "number", // 商品ID
  "quantity": "number" // 数量 (默认: 1)
}
```

## 📦 订单接口

### 获取订单列表

```http
GET /api/orders
```

**需要认证**: ✅

**查询参数:**

- `page`: 页码
- `pageSize`: 每页数量
- `status`: 订单状态
- `paymentStatus`: 支付状态
- `startDate`: 开始日期
- `endDate`: 结束日期

### 创建订单

```http
POST /api/orders
```

**需要认证**: ✅

**请求参数:**

```json
{
  "items": [
    {
      "productId": "number",
      "quantity": "number"
    }
  ],
  "shippingAddressId": "number",
  "remark": "string"
}
```

## 📱 社交接口

### 获取动态列表

```http
GET /api/posts
```

**查询参数:**

- `page`: 页码
- `pageSize`: 每页数量
- `userId`: 用户ID (获取指定用户的动态)
- `type`: 动态类型 (TEXT/IMAGE/PRODUCT_SHARE)
- `keyword`: 搜索关键词
- `sortBy`: 排序字段 (createdAt/likesCount/commentsCount)

### 发布动态

```http
POST /api/posts
```

**需要认证**: ✅

**请求参数:**

```json
{
  "content": "string", // 动态内容
  "images": ["string"], // 图片链接数组 (可选)
  "type": "string", // 动态类型
  "productId": "number" // 商品ID (商品分享时必填)
}
```

### 关注/取消关注用户

```http
POST /api/users/:id/follow
```

**需要认证**: ✅

**路径参数:**

- `id`: 目标用户ID

## 📊 日志管理接口

> **权限要求**: 管理员权限

### 获取搜索日志列表

```http
GET /api/admin/logs/search
```

**查询参数:**

| 参数       | 类型   | 必填 | 说明                |
| ---------- | ------ | ---- | ------------------- |
| page       | number | 否   | 页码，默认1         |
| pageSize   | number | 否   | 每页数量，默认20    |
| keyword    | string | 否   | 搜索关键词过滤      |
| userId     | number | 否   | 用户ID过滤          |
| startDate  | string | 否   | 开始时间 (ISO格式)  |
| endDate    | string | 否   | 结束时间 (ISO格式)  |
| minResults | number | 否   | 最小结果数          |
| maxResults | number | 否   | 最大结果数          |
| sortBy     | string | 否   | 排序字段            |
| sortOrder  | string | 否   | 排序方向 (asc/desc) |

**响应数据:**

```json
{
  "success": true,
  "message": "获取搜索日志成功",
  "data": {
    "items": [
      {
        "id": 1,
        "keyword": "手机",
        "resultCount": 25,
        "userId": 123,
        "userAgent": "Mozilla/5.0...",
        "ipAddress": "***********",
        "filters": {
          "categoryId": 1,
          "minPrice": 1000
        },
        "createdAt": "2024-01-01T10:00:00.000Z",
        "user": {
          "id": 123,
          "username": "user123",
          "nickname": "用户123",
          "email": "<EMAIL>"
        }
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 20,
    "totalPages": 5,
    "stats": {
      "totalSearches": 1000,
      "averageResults": 15.5,
      "totalResults": 15500
    },
    "popularKeywords": [
      {
        "keyword": "手机",
        "count": 150
      }
    ]
  }
}
```

### 获取用户操作日志列表

```http
GET /api/admin/logs/users
```

**查询参数:**

| 参数       | 类型   | 必填 | 说明                |
| ---------- | ------ | ---- | ------------------- |
| page       | number | 否   | 页码，默认1         |
| pageSize   | number | 否   | 每页数量，默认20    |
| userId     | number | 否   | 用户ID过滤          |
| action     | string | 否   | 操作类型过滤        |
| resource   | string | 否   | 资源类型过滤        |
| resourceId | number | 否   | 资源ID过滤          |
| startDate  | string | 否   | 开始时间 (ISO格式)  |
| endDate    | string | 否   | 结束时间 (ISO格式)  |
| ipAddress  | string | 否   | IP地址过滤          |
| sortBy     | string | 否   | 排序字段            |
| sortOrder  | string | 否   | 排序方向 (asc/desc) |

**响应数据:**

```json
{
  "success": true,
  "message": "获取用户日志成功",
  "data": {
    "items": [
      {
        "id": 1,
        "userId": 123,
        "action": "LOGIN",
        "resource": null,
        "resourceId": null,
        "details": {
          "loginMethod": "email",
          "success": true,
          "timestamp": "2024-01-01T10:00:00.000Z"
        },
        "userAgent": "Mozilla/5.0...",
        "ipAddress": "***********",
        "createdAt": "2024-01-01T10:00:00.000Z",
        "user": {
          "id": 123,
          "username": "user123",
          "nickname": "用户123",
          "email": "<EMAIL>",
          "role": "USER"
        }
      }
    ],
    "total": 500,
    "page": 1,
    "pageSize": 20,
    "totalPages": 25,
    "stats": {
      "actionStats": [
        {
          "action": "LOGIN",
          "count": 50
        }
      ],
      "activeUsersCount": 25,
      "topActiveUsers": [
        {
          "userId": 123,
          "actionCount": 15
        }
      ]
    }
  }
}
```

### 获取日志统计信息

```http
GET /api/admin/logs/stats
```

**查询参数:**

| 参数 | 类型   | 必填 | 说明              |
| ---- | ------ | ---- | ----------------- |
| days | number | 否   | 统计天数，默认7天 |

**响应数据:**

```json
{
  "success": true,
  "message": "获取日志统计成功",
  "data": {
    "period": {
      "days": 7,
      "startDate": "2024-01-01T00:00:00.000Z",
      "endDate": "2024-01-08T00:00:00.000Z"
    },
    "searchStats": {
      "totalSearches": 1000,
      "averageResults": 15.5,
      "totalResults": 15500
    },
    "userActivityStats": {
      "totalActions": 5000,
      "uniqueActiveUsers": 200
    },
    "trends": {
      "dailySearches": [
        {
          "date": "2024-01-01",
          "count": 150,
          "avg_results": 16.2
        }
      ],
      "dailyUserActivity": [
        {
          "date": "2024-01-01",
          "count": 800,
          "unique_users": 45
        }
      ]
    },
    "popularKeywords": [
      {
        "keyword": "手机",
        "searchCount": 150,
        "averageResults": 20.5
      }
    ],
    "actionTypeStats": [
      {
        "action": "LOGIN",
        "count": 300
      }
    ],
    "topActiveUsers": [
      {
        "userId": 123,
        "actionCount": 25,
        "user": {
          "id": 123,
          "username": "user123",
          "nickname": "用户123",
          "email": "<EMAIL>"
        }
      }
    ],
    "topIPs": [
      {
        "ipAddress": "***********",
        "actionCount": 100
      }
    ]
  }
}
```

### 清理过期日志

```http
POST /api/admin/logs/cleanup
```

**请求体:**

```json
{
  "daysToKeep": 90,
  "logTypes": ["search", "user"]
}
```

**参数说明:**

| 参数       | 类型   | 必填 | 说明                                |
| ---------- | ------ | ---- | ----------------------------------- |
| daysToKeep | number | 否   | 保留天数，默认90天                  |
| logTypes   | array  | 否   | 日志类型，可选值: search, user, all |

**响应数据:**

```json
{
  "success": true,
  "message": "日志清理完成",
  "data": {
    "daysToKeep": 90,
    "cutoffDate": "2023-10-01T00:00:00.000Z",
    "searchLogsDeleted": 1500,
    "userLogsDeleted": 3000,
    "totalDeleted": 4500
  }
}
```

### 用户操作类型枚举

日志系统支持以下用户操作类型：

| 操作类型        | 说明         | 资源类型 |
| --------------- | ------------ | -------- |
| LOGIN           | 登录         | -        |
| LOGOUT          | 登出         | -        |
| REGISTER        | 注册         | -        |
| PASSWORD_CHANGE | 修改密码     | -        |
| PROFILE_UPDATE  | 更新个人信息 | -        |
| PRODUCT_VIEW    | 查看商品     | product  |
| PRODUCT_SEARCH  | 搜索商品     | -        |
| ORDER_CREATE    | 创建订单     | order    |
| ORDER_PAY       | 支付订单     | order    |
| ORDER_CANCEL    | 取消订单     | order    |
| CART_ADD        | 添加到购物车 | product  |
| CART_REMOVE     | 从购物车移除 | product  |
| FAVORITE_ADD    | 添加收藏     | product  |
| FAVORITE_REMOVE | 移除收藏     | product  |
| REVIEW_CREATE   | 创建评价     | product  |
| COUPON_CLAIM    | 领取优惠券   | coupon   |
| COUPON_USE      | 使用优惠券   | coupon   |
| POST_CREATE     | 创建动态     | post     |
| POST_LIKE       | 点赞动态     | post     |
| COMMENT_CREATE  | 创建评论     | comment  |
| FOLLOW_USER     | 关注用户     | user     |
| UNFOLLOW_USER   | 取消关注     | user     |

### 定时清理日志

```http
POST /api/cron/cleanup-logs
```

> **权限要求**: 定时任务密钥或管理员权限

**请求头:**

```http
Authorization: Bearer <cron-secret>
```

**响应数据:**

```json
{
  "success": true,
  "message": "日志清理完成",
  "data": {
    "searchLogsDeleted": 1500,
    "userLogsDeleted": 3000,
    "totalDeleted": 4500
  }
}
```

**说明:**

- 此接口用于定时任务自动清理过期日志
- 需要在请求头中提供 `CRON_SECRET` 环境变量中配置的密钥
- 或者使用管理员权限访问
- 默认清理90天前的日志（可通过 `LOG_RETENTION_DAYS` 环境变量配置）

## 🔍 系统接口

### 健康检查

```http
GET /api/health
```

**响应数据:**

```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "services": {
    "database": {
      "status": "healthy",
      "responseTime": 10
    },
    "redis": "connected"
  },
  "version": "1.0.0"
}
```

## 📝 状态码说明

- `200`: 成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `409`: 资源冲突
- `422`: 业务逻辑错误
- `500`: 服务器内部错误

## 🔧 错误处理

API使用统一的错误处理机制，所有错误都会返回标准的错误响应格式。

### 常见错误类型

1. **ValidationError (400)**: 请求数据验证失败
2. **AuthenticationError (401)**: 认证失败
3. **AuthorizationError (403)**: 权限不足
4. **NotFoundError (404)**: 资源未找到
5. **ConflictError (409)**: 资源冲突
6. **BusinessError (422)**: 业务逻辑错误

## 📚 使用示例

### JavaScript/TypeScript

```javascript
// 登录
const response = await fetch('/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'testuser',
    password: '123456'
  })
})

const result = await response.json()
if (result.success) {
  const token = result.data.token
  // 保存token用于后续请求
}

// 获取商品列表
const productsResponse = await fetch('/api/products?page=1&pageSize=10')
const productsResult = await productsResponse.json()

// 获取搜索日志（管理员）
const searchLogsResponse = await fetch('/api/admin/logs/search?page=1&pageSize=20', {
  headers: {
    Authorization: `Bearer ${adminToken}`
  }
})
const searchLogsResult = await searchLogsResponse.json()

// 获取用户操作日志（管理员）
const userLogsResponse = await fetch('/api/admin/logs/users?action=LOGIN', {
  headers: {
    Authorization: `Bearer ${adminToken}`
  }
})
const userLogsResult = await userLogsResponse.json()

// 获取日志统计信息（管理员）
const statsResponse = await fetch('/api/admin/logs/stats?days=7', {
  headers: {
    Authorization: `Bearer ${adminToken}`
  }
})
const statsResult = await statsResponse.json()

// 清理过期日志（管理员）
const cleanupResponse = await fetch('/api/admin/logs/cleanup', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${adminToken}`
  },
  body: JSON.stringify({
    daysToKeep: 90,
    logTypes: ['search', 'user']
  })
})
const cleanupResult = await cleanupResponse.json()
```

### cURL

```bash
# 登录
curl -X POST /api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"123456"}'

# 登出
curl -X POST /api/auth/logout \
  -H "Authorization: Bearer <your-token>"

# 检查用户名是否可用
curl -X GET "/api/auth/check-username?username=testuser"

# 检查邮箱是否可用
curl -X GET "/api/auth/check-email?email=<EMAIL>"

# 获取个人信息
curl -X GET /api/users/profile \
  -H "Authorization: Bearer <your-token>"

# 搜索商品
curl -X GET "/api/products/search?q=手机&page=1&pageSize=10&sortBy=price&sortOrder=asc"

# 获取搜索建议
curl -X GET "/api/search/suggestions?q=手机&limit=5"

# 获取热门搜索关键词
curl -X GET "/api/search/suggestions?limit=10"

# 获取商品详情
curl -X GET /api/products/1

# 获取搜索日志（管理员）
curl -X GET "/api/admin/logs/search?page=1&pageSize=20&keyword=手机" \
  -H "Authorization: Bearer <admin-token>"

# 获取用户操作日志（管理员）
curl -X GET "/api/admin/logs/users?page=1&pageSize=20&action=LOGIN" \
  -H "Authorization: Bearer <admin-token>"

# 获取日志统计信息（管理员）
curl -X GET "/api/admin/logs/stats?days=7" \
  -H "Authorization: Bearer <admin-token>"

# 清理过期日志（管理员）
curl -X POST /api/admin/logs/cleanup \
  -H "Authorization: Bearer <admin-token>" \
  -H "Content-Type: application/json" \
  -d '{"daysToKeep":90,"logTypes":["search","user"]}'

# 定时清理日志（定时任务）
curl -X POST /api/cron/cleanup-logs \
  -H "Authorization: Bearer <cron-secret>"
```

## 🚀 开发建议

1. **错误处理**: 始终检查响应的`success`字段
2. **认证**: 在需要认证的接口中包含JWT token
3. **分页**: 使用分页参数避免一次性加载大量数据
4. **缓存**: 对不经常变化的数据进行适当缓存
5. **重试**: 对网络错误实现重试机制
6. **日志监控**:
   - 定期检查日志统计信息，了解系统使用情况
   - 监控异常操作和可疑行为
   - 设置定时任务自动清理过期日志
   - 根据业务需求调整日志保留策略
7. **权限控制**: 日志管理接口仅限管理员访问，确保数据安全
8. **性能优化**: 日志查询支持多种过滤条件，合理使用以提高查询效率
