# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
charset = utf-8

# 2 space indentation
[*.{js,ts,vue,json,yml,yaml,md}]
indent_style = space
indent_size = 2

# 4 space indentation
[*.{py,php,java,cs}]
indent_style = space
indent_size = 4

# Tab indentation (no size specified)
[Makefile]
indent_style = tab

# Matches the exact files either package.json or .travis.yml
[{package.json,.travis.yml}]
indent_style = space
indent_size = 2

# SQL files
[*.sql]
indent_style = space
indent_size = 2

# Prisma schema files
[*.prisma]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = off
