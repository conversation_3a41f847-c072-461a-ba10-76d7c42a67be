/**
 * 获取动态列表
 * GET /api/posts
 */

export default defineApiHandler(async (event) => {
  const query = getQuery(event)
  
  // 解析分页参数
  const { page, pageSize, skip, take } = parsePaginationQuery(query)
  
  // 解析排序参数
  const allowedSortFields = ['createdAt', 'likesCount', 'commentsCount']
  const orderBy = parseSortQuery(query, allowedSortFields)
  
  // 构建查询条件
  const where: any = {
    status: 'PUBLISHED'
  }
  
  // 用户过滤
  if (query.userId) {
    where.userId = parseInt(query.userId as string)
  }
  
  // 动态类型过滤
  if (query.type) {
    where.type = query.type
  }
  
  // 关键词搜索
  if (query.keyword) {
    where.content = {
      contains: query.keyword as string,
      mode: 'insensitive'
    }
  }
  
  // 获取当前用户ID（用于判断点赞状态）
  const currentUserId = event.context.user?.id
  
  // 查询动态列表和总数
  const [posts, total] = await Promise.all([
    prisma.post.findMany({
      where,
      orderBy,
      skip,
      take,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            nickname: true,
            avatar: true
          }
        },
        product: query.type === 'PRODUCT_SHARE' ? {
          select: {
            id: true,
            name: true,
            price: true,
            images: true,
            status: true
          }
        } : false,
        comments: {
          take: 3,
          orderBy: { createdAt: 'desc' },
          include: {
            user: {
              select: {
                id: true,
                username: true,
                nickname: true,
                avatar: true
              }
            }
          }
        },
        likes: currentUserId ? {
          where: { userId: currentUserId },
          select: { id: true }
        } : false
      }
    }),
    prisma.post.count({ where })
  ])
  
  // 格式化动态数据
  const formattedPosts = posts.map(post => ({
    ...post,
    isLiked: currentUserId ? post.likes.length > 0 : false,
    product: post.product ? {
      ...post.product,
      price: post.product.price?.toNumber()
    } : null,
    likes: undefined
  }))
  
  return createPaginatedResponse(
    formattedPosts,
    total,
    page,
    pageSize,
    '获取动态列表成功'
  )
})
