@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 解析命令行参数
set SKIP_CONFIG=false
set SILENT=false

:parse_args
if "%~1"=="" goto :args_done
if "%~1"=="--skip-config" (
    set SKIP_CONFIG=true
    shift
    goto :parse_args
)
if "%~1"=="--silent" (
    set SILENT=true
    shift
    goto :parse_args
)
if "%~1"=="-h" goto :show_help
if "%~1"=="--help" goto :show_help
echo 未知选项: %~1
echo 使用 %~0 --help 查看帮助
exit /b 1

:show_help
echo 用法: %~0 [选项]
echo 选项:
echo   --skip-config    跳过数据库和Redis配置
echo   --silent         静默模式，使用默认配置
echo   -h, --help       显示帮助信息
exit /b 0

:args_done

echo 🚀 开始项目初始化...
echo.

REM 检查必要的工具
echo 📋 检查必要工具...

where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js: https://nodejs.org/
    pause
    exit /b 1
) else (
    echo ✅ Node.js 已安装
)

where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ npm 未安装，请先安装 npm
    pause
    exit /b 1
) else (
    echo ✅ npm 已安装
)

where git >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Git 未安装，请先安装 Git: https://git-scm.com/
    pause
    exit /b 1
) else (
    echo ✅ Git 已安装
)

echo.

REM 检查是否需要安装依赖
if not exist "node_modules" (
    echo 📦 安装项目依赖...
    call npm install
    if !errorlevel! neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 项目依赖已存在
)

echo.

REM 创建上传目录
echo 📁 创建上传目录...
if not exist "public\uploads\images" mkdir "public\uploads\images"
if not exist "public\uploads\avatars" mkdir "public\uploads\avatars"
if not exist "public\uploads\documents" mkdir "public\uploads\documents"

REM 创建.gitkeep文件
echo. > "public\uploads\images\.gitkeep"
echo. > "public\uploads\avatars\.gitkeep"
echo. > "public\uploads\documents\.gitkeep"

echo ✅ 上传目录创建完成

echo.

REM 检查环境变量文件
if not exist ".env" (
    if exist ".env.example" (
        echo 📝 创建环境变量文件...
        copy ".env.example" ".env" >nul
        echo ✅ 环境变量文件已创建
    ) else (
        echo ⚠️  未找到 .env.example 文件，请手动创建 .env 文件
    )
) else (
    echo ✅ 环境变量文件已存在
)

echo.

REM 询问是否需要配置数据库和Redis
if "%SKIP_CONFIG%"=="false" if "%SILENT%"=="false" (
    echo 🔧 数据库和Redis配置
    echo.

    set /p configure_db="是否需要配置数据库连接？(y/N): "
    if /i "!configure_db!"=="y" (
        call :configure_database
        echo.
    )

    set /p configure_redis="是否需要配置Redis连接？(y/N): "
    if /i "!configure_redis!"=="y" (
        call :configure_redis
        echo.
    )

    if not "!configure_db!"=="y" if not "!configure_redis!"=="y" (
        echo ⚠️  跳过配置，请手动编辑 .env 文件
        echo.
    )
) else if "%SKIP_CONFIG%"=="true" (
    echo ⚠️  跳过数据库和Redis配置（使用 --skip-config 参数）
    echo.
) else if "%SILENT%"=="true" (
    echo 🔧 使用默认配置（静默模式）
    call :configure_database
    call :configure_redis
    echo.
)

goto :continue_setup

:test_database_connection
echo 🔗 测试数据库连接...

REM 尝试连接数据库
call npx prisma db push --accept-data-loss >nul 2>nul
if !errorlevel! equ 0 (
    echo ✅ 数据库连接成功
    exit /b 0
) else (
    echo ❌ 数据库连接失败

    REM 提供诊断信息
    echo 🔍 诊断信息：

    REM 从.env文件读取数据库URL
    for /f "tokens=2 delims==" %%a in ('findstr "^DATABASE_URL=" .env 2^>nul') do (
        set db_url=%%a
        set db_url=!db_url:"=!
    )

    REM 简单的URL解析（基础版本）
    if defined db_url (
        echo   数据库URL: !db_url!

        REM 尝试提取主机和端口信息
        echo !db_url! | findstr "localhost" >nul
        if !errorlevel! equ 0 (
            echo   检查本地PostgreSQL服务是否启动
        )
    ) else (
        echo   ❌ 未找到数据库URL配置
    )

    exit /b 1
)
goto :eof

:configure_database
echo 🗄️  配置数据库连接...
echo.

REM 默认值
set db_host=localhost
set db_port=5432
set db_user=postgres
set db_password=
set db_name=shop_db

if "%SILENT%"=="false" (
    echo 请输入数据库连接信息（按回车使用默认值）：

    set /p input_host="数据库主机 [%db_host%]: "
    if not "!input_host!"=="" set db_host=!input_host!

    set /p input_port="数据库端口 [%db_port%]: "
    if not "!input_port!"=="" set db_port=!input_port!

    set /p input_user="数据库用户名 [%db_user%]: "
    if not "!input_user!"=="" set db_user=!input_user!

    set /p input_password="数据库密码: "
    if not "!input_password!"=="" set db_password=!input_password!

    set /p input_name="数据库名称 [%db_name%]: "
    if not "!input_name!"=="" set db_name=!input_name!
)

REM 构建数据库URL
set database_url=postgresql://!db_user!
if not "!db_password!"=="" (
    set database_url=!database_url!:!db_password!
)
set database_url=!database_url!@!db_host!:!db_port!/!db_name!

REM 更新.env文件
if exist ".env" (
    powershell -Command "(Get-Content .env) -replace '^DATABASE_URL=.*', 'DATABASE_URL=\"!database_url!\"' | Set-Content .env"
    powershell -Command "(Get-Content .env) -replace '^DB_HOST=.*', 'DB_HOST=!db_host!' | Set-Content .env"
    powershell -Command "(Get-Content .env) -replace '^DB_PORT=.*', 'DB_PORT=!db_port!' | Set-Content .env"
    powershell -Command "(Get-Content .env) -replace '^DB_USER=.*', 'DB_USER=!db_user!' | Set-Content .env"
    powershell -Command "(Get-Content .env) -replace '^DB_NAME=.*', 'DB_NAME=!db_name!' | Set-Content .env"

    if not "!db_password!"=="" (
        powershell -Command "(Get-Content .env) -replace '^DB_PASSWORD=.*', 'DB_PASSWORD=!db_password!' | Set-Content .env"
    )

    echo ✅ 数据库配置已更新
) else (
    echo ❌ .env 文件不存在
)
goto :eof

:configure_redis
echo 📦 配置Redis连接...
echo.

REM 默认值
set redis_host=localhost
set redis_port=6379
set redis_password=
set redis_db=0

if "%SILENT%"=="false" (
    echo 请输入Redis连接信息（按回车使用默认值）：

    set /p input_host="Redis主机 [%redis_host%]: "
    if not "!input_host!"=="" set redis_host=!input_host!

    set /p input_port="Redis端口 [%redis_port%]: "
    if not "!input_port!"=="" set redis_port=!input_port!

    set /p input_password="Redis密码（可选）: "
    if not "!input_password!"=="" set redis_password=!input_password!

    set /p input_db="Redis数据库编号 [%redis_db%]: "
    if not "!input_db!"=="" set redis_db=!input_db!
)

REM 构建Redis URL
set redis_url=redis://
if not "!redis_password!"=="" (
    set redis_url=!redis_url!:!redis_password!@
)
set redis_url=!redis_url!!redis_host!:!redis_port!/!redis_db!

REM 更新.env文件
if exist ".env" (
    powershell -Command "(Get-Content .env) -replace '^REDIS_URL=.*', 'REDIS_URL=!redis_url!' | Set-Content .env"
    echo ✅ Redis配置已更新
) else (
    echo ❌ .env 文件不存在
)
goto :eof

:continue_setup

REM 数据库初始化
echo 🗄️  初始化数据库...

REM 检查是否有Prisma配置
if not exist "prisma\schema.prisma" (
    echo ❌ 未找到 Prisma 配置文件
    pause
    exit /b 1
)

REM 生成 Prisma Client
echo 📦 生成 Prisma Client...
call npx prisma generate
if !errorlevel! neq 0 (
    echo ❌ 生成 Prisma Client 失败
    pause
    exit /b 1
)

REM 验证配置
echo 🔍 验证配置...

if not exist ".env" (
    echo ❌ .env 文件不存在
    goto :config_failed
)

REM 检查必要的配置项
findstr /B "DATABASE_URL=" .env >nul
if !errorlevel! neq 0 (
    echo ⚠️  缺少必要的配置项: DATABASE_URL
    goto :config_failed
)

findstr /B "JWT_SECRET=" .env >nul
if !errorlevel! neq 0 (
    echo ⚠️  缺少必要的配置项: JWT_SECRET
    goto :config_failed
)

echo ✅ 配置验证通过

REM 数据库连接和初始化循环
set database_init_success=false
set max_retries=3
set retry_count=0

:database_retry_loop
if !retry_count! geq !max_retries! goto :database_failed

call :test_database_connection
if !errorlevel! equ 0 (

    REM 推送数据库架构
    echo 🗄️  推送数据库架构...
    call npx prisma db push
    if !errorlevel! neq 0 (
        echo ❌ 推送数据库架构失败
        pause
        exit /b 1
    )

    REM 填充种子数据
    echo 🌱 填充种子数据...
    call npx prisma db seed
    if !errorlevel! neq 0 (
        echo ⚠️  种子数据填充失败，可能是数据已存在
    ) else (
        echo ✅ 种子数据填充完成
    )

    set database_init_success=true
    goto :database_success
) else (
    set /a retry_count+=1

    if !retry_count! lss !max_retries! (
        if "%SILENT%"=="false" (
            echo.
            echo 可能的原因：
            echo 1. 数据库服务未启动
            echo 2. 数据库连接信息错误
            echo 3. 数据库用户权限不足
            echo 4. 网络连接问题
            echo.

            set /p reconfigure_db="是否重新配置数据库连接？(y/N): "
            if /i "!reconfigure_db!"=="y" (
                echo.
                call :configure_database
                echo.
                goto :database_retry_loop
            ) else (
                echo ⚠️  跳过数据库初始化
                goto :database_failed
            )
        ) else (
            echo ⚠️  静默模式下跳过重试
            goto :database_failed
        )
    ) else (
        echo ❌ 已达到最大重试次数 (!max_retries!)
        echo ⚠️  数据库初始化失败，请检查配置后手动运行: npm run db:init:win
        goto :database_failed
    )
)

:database_success
goto :continue_after_database

:database_failed

:continue_after_database

echo.

REM 完成提示
echo 🎉 项目初始化完成！
echo.
echo 📋 接下来的步骤：
echo 1. 检查并配置 .env 文件中的环境变量
echo 2. 确保数据库服务正在运行
echo 3. 运行 npm run dev 启动开发服务器
echo.
echo 📋 测试账号信息：
echo 管理员: <EMAIL> / 123456
echo 商家: <EMAIL> / 123456
echo 用户: <EMAIL> / 123456
echo.
echo 🛠️  常用命令：
echo npm run dev          # 启动开发服务器
echo npm run db:studio    # 打开数据库管理界面
echo npm run db:reset     # 重置数据库
echo npm run build        # 构建生产版本
echo.
echo 🌐 现在可以运行 npm run dev 启动应用！
echo.
pause
goto :end

:config_failed
echo ⚠️  配置验证失败，跳过数据库初始化
echo    请手动配置 .env 文件后运行: npm run db:init:win
echo.
echo 🎉 项目基础初始化完成！
echo.
pause
goto :end

:end
