<template>
  <UForm 
    ref="formRef"
    :schema="loginSchema" 
    :state="formData" 
    @submit="handleSubmit"
    class="space-y-6"
  >
    <!-- 用户名/邮箱 -->
    <UFormGroup 
      label="用户名或邮箱" 
      name="username"
      required
    >
      <UInput
        v-model="formData.username"
        placeholder="请输入用户名或邮箱"
        size="lg"
        :disabled="isLoading"
        autocomplete="username"
      />
    </UFormGroup>

    <!-- 密码 -->
    <UFormGroup 
      label="密码" 
      name="password"
      required
    >
      <UInput
        v-model="formData.password"
        type="password"
        placeholder="请输入密码"
        size="lg"
        :disabled="isLoading"
        autocomplete="current-password"
      />
    </UFormGroup>

    <!-- 记住我和忘记密码 -->
    <div class="flex items-center justify-between">
      <UCheckbox
        v-model="formData.rememberMe"
        label="记住我"
        :disabled="isLoading"
      />
      <NuxtLink 
        to="/forgot-password" 
        class="text-sm text-primary-600 hover:text-primary-500 transition-colors"
      >
        忘记密码？
      </NuxtLink>
    </div>

    <!-- 登录按钮 -->
    <UButton
      type="submit"
      size="lg"
      block
      :loading="isLoading"
      :disabled="!isFormValid"
    >
      {{ isLoading ? '登录中...' : '登录' }}
    </UButton>
  </UForm>
</template>

<script setup lang="ts">
import { z } from 'zod'
import type { LoginForm } from '~/types'

// 组件属性
interface Props {
  isLoading?: boolean
}

// 组件事件
interface Emits {
  submit: [data: LoginForm]
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false
})

const emit = defineEmits<Emits>()

// 表单数据
const formData = reactive<LoginForm>({
  username: '',
  password: '',
  rememberMe: false
})

// 表单验证规则
const loginSchema = z.object({
  username: z.string().min(1, '请输入用户名或邮箱'),
  password: z.string().min(1, '请输入密码'),
  rememberMe: z.boolean().optional()
})

// 表单引用
const formRef = ref()

// 计算属性
const isFormValid = computed(() => {
  return formData.username.trim() && formData.password.trim()
})

// 处理表单提交
const handleSubmit = () => {
  emit('submit', { ...formData })
}

// 重置表单
const resetForm = () => {
  formData.username = ''
  formData.password = ''
  formData.rememberMe = false
}

// 聚焦到第一个输入框
const focusFirstInput = () => {
  nextTick(() => {
    const firstInput = formRef.value?.$el.querySelector('input')
    if (firstInput) {
      firstInput.focus()
    }
  })
}

// 暴露方法给父组件
defineExpose({
  resetForm,
  focusFirstInput
})

// 组件挂载时聚焦
onMounted(() => {
  focusFirstInput()
})
</script>
