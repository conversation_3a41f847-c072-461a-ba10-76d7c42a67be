#!/bin/bash

# 社交购物网站开发环境设置脚本

set -e

echo "🚀 开始设置开发环境..."

# 检查Node.js版本
echo "📦 检查Node.js版本..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js 18+版本"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js版本过低，需要18+版本，当前版本: $(node -v)"
    exit 1
fi
echo "✅ Node.js版本检查通过: $(node -v)"

# 检查包管理器
echo "📦 检查包管理器..."
if command -v pnpm &> /dev/null; then
    PKG_MANAGER="pnpm"
elif command -v yarn &> /dev/null; then
    PKG_MANAGER="yarn"
else
    PKG_MANAGER="npm"
fi
echo "✅ 使用包管理器: $PKG_MANAGER"

# 安装依赖
echo "📦 安装项目依赖..."
$PKG_MANAGER install

# 检查Docker
echo "🐳 检查Docker环境..."
if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
    echo "✅ Docker环境可用"
    
    # 启动开发服务
    echo "🚀 启动开发服务..."
    docker-compose -f docker-compose.dev.yml up -d
    
    # 等待服务启动
    echo "⏳ 等待服务启动..."
    sleep 10
    
    # 检查服务状态
    echo "🔍 检查服务状态..."
    docker-compose -f docker-compose.dev.yml ps
else
    echo "⚠️  Docker未安装，请手动配置PostgreSQL和Redis"
fi

# 复制环境变量文件
echo "⚙️  配置环境变量..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ 已创建.env文件，请根据需要修改配置"
else
    echo "✅ .env文件已存在"
fi

# 设置Git hooks
echo "🔧 设置Git hooks..."
if [ -d .git ]; then
    $PKG_MANAGER run prepare
    echo "✅ Git hooks设置完成"
else
    echo "⚠️  不是Git仓库，跳过Git hooks设置"
fi

# 生成Prisma客户端
echo "🗄️  生成Prisma客户端..."
$PKG_MANAGER run db:generate

# 运行数据库迁移
echo "🗄️  运行数据库迁移..."
if docker-compose -f docker-compose.dev.yml ps postgres | grep -q "Up"; then
    echo "⏳ 等待数据库就绪..."
    sleep 5
    $PKG_MANAGER run db:migrate
    
    # 填充测试数据
    echo "🌱 填充测试数据..."
    $PKG_MANAGER run db:seed
    echo "✅ 测试数据填充完成"
else
    echo "⚠️  数据库未启动，请手动运行迁移: npm run db:migrate"
fi

# 运行代码检查
echo "🔍 运行代码检查..."
$PKG_MANAGER run lint
$PKG_MANAGER run type-check

echo ""
echo "🎉 开发环境设置完成！"
echo ""
echo "📋 可用的命令:"
echo "  $PKG_MANAGER run dev          - 启动开发服务器"
echo "  $PKG_MANAGER run db:studio    - 打开Prisma Studio"
echo "  $PKG_MANAGER run lint         - 运行代码检查"
echo "  $PKG_MANAGER run format       - 格式化代码"
echo "  $PKG_MANAGER run type-check   - 类型检查"
echo ""
echo "🌐 服务地址:"
echo "  应用: http://localhost:3000"
echo "  pgAdmin: http://localhost:5050 (<EMAIL> / admin123)"
echo "  Redis Commander: http://localhost:8081 (admin / admin123)"
echo "  MailHog: http://localhost:8025"
echo ""
echo "🔑 测试账号:"
echo "  管理员: <EMAIL> / 123456"
echo "  商家: <EMAIL> / 123456"
echo "  用户: <EMAIL> / 123456"
echo ""
echo "🚀 运行 '$PKG_MANAGER run dev' 启动开发服务器"
