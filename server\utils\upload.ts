/**
 * 文件上传工具函数
 */

import { promises as fs } from 'fs'
import path from 'path'
import { v4 as uuidv4 } from 'uuid'

// 文件上传配置
export interface UploadConfig {
  allowedTypes: string[]
  maxFileSize: number
  uploadDir: string
  generateFileName?: (originalName: string) => string
}

// 默认配置
export const defaultUploadConfigs = {
  images: {
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
    maxFileSize: 5 * 1024 * 1024, // 5MB
    uploadDir: 'public/uploads/images',
    generateFileName: (originalName: string) => {
      const ext = path.extname(originalName)
      return `${uuidv4()}${ext}`
    }
  },
  documents: {
    allowedTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    maxFileSize: 10 * 1024 * 1024, // 10MB
    uploadDir: 'public/uploads/documents',
    generateFileName: (originalName: string) => {
      const ext = path.extname(originalName)
      const name = path.basename(originalName, ext).replace(/[^a-zA-Z0-9]/g, '_')
      return `${name}_${Date.now()}${ext}`
    }
  },
  avatars: {
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png'],
    maxFileSize: 2 * 1024 * 1024, // 2MB
    uploadDir: 'public/uploads/avatars',
    generateFileName: (originalName: string) => {
      const ext = path.extname(originalName)
      return `avatar_${uuidv4()}${ext}`
    }
  }
}

// 上传单个文件
export async function uploadFile(
  file: { filename?: string; type?: string; data: Buffer },
  config: UploadConfig
): Promise<string> {
  if (!file.filename || !file.data) {
    throw new ValidationError('无效的文件数据')
  }

  // 验证文件类型
  const fileType = file.type || ''
  if (!config.allowedTypes.includes(fileType)) {
    throw new ValidationError(`不支持的文件格式: ${fileType}。支持的格式: ${config.allowedTypes.join(', ')}`)
  }

  // 验证文件大小
  if (file.data.length > config.maxFileSize) {
    const maxSizeMB = config.maxFileSize / 1024 / 1024
    throw new ValidationError(`文件大小不能超过 ${maxSizeMB}MB`)
  }

  // 确保上传目录存在
  const uploadDir = path.join(process.cwd(), config.uploadDir)
  await fs.mkdir(uploadDir, { recursive: true })

  // 生成文件名
  const fileName = config.generateFileName 
    ? config.generateFileName(file.filename)
    : `${uuidv4()}${path.extname(file.filename)}`

  const filePath = path.join(uploadDir, fileName)

  try {
    // 保存文件
    await fs.writeFile(filePath, file.data)
    
    // 返回相对路径
    const relativePath = config.uploadDir.replace('public', '') + '/' + fileName
    return relativePath

  } catch (error) {
    console.error('保存文件失败:', error)
    throw new InternalServerError('文件保存失败')
  }
}

// 上传多个文件
export async function uploadFiles(
  files: { filename?: string; type?: string; data: Buffer }[],
  config: UploadConfig
): Promise<string[]> {
  const uploadedFiles: string[] = []

  for (const file of files) {
    if (file.filename && file.data) {
      try {
        const filePath = await uploadFile(file, config)
        uploadedFiles.push(filePath)
      } catch (error) {
        console.error(`上传文件 ${file.filename} 失败:`, error)
        // 继续处理其他文件，但记录错误
      }
    }
  }

  return uploadedFiles
}

// 删除文件
export async function deleteFile(filePath: string): Promise<void> {
  try {
    const fullPath = path.join(process.cwd(), 'public', filePath)
    await fs.unlink(fullPath)
    console.log(`文件删除成功: ${filePath}`)
  } catch (error) {
    console.error(`删除文件失败: ${filePath}`, error)
    // 不抛出错误，因为文件可能已经不存在
  }
}

// 验证文件是否存在
export async function fileExists(filePath: string): Promise<boolean> {
  try {
    const fullPath = path.join(process.cwd(), 'public', filePath)
    await fs.access(fullPath)
    return true
  } catch {
    return false
  }
}

// 获取文件信息
export async function getFileInfo(filePath: string) {
  try {
    const fullPath = path.join(process.cwd(), 'public', filePath)
    const stats = await fs.stat(fullPath)
    
    return {
      size: stats.size,
      createdAt: stats.birthtime,
      modifiedAt: stats.mtime,
      isFile: stats.isFile(),
      isDirectory: stats.isDirectory()
    }
  } catch (error) {
    throw new NotFoundError('文件不存在')
  }
}

// 清理过期文件（可用于定时任务）
export async function cleanupExpiredFiles(
  directory: string,
  maxAge: number = 7 * 24 * 60 * 60 * 1000 // 默认7天
): Promise<number> {
  try {
    const fullDir = path.join(process.cwd(), 'public', directory)
    const files = await fs.readdir(fullDir)
    let deletedCount = 0

    for (const file of files) {
      const filePath = path.join(fullDir, file)
      const stats = await fs.stat(filePath)
      
      if (Date.now() - stats.mtime.getTime() > maxAge) {
        await fs.unlink(filePath)
        deletedCount++
        console.log(`清理过期文件: ${file}`)
      }
    }

    return deletedCount
  } catch (error) {
    console.error('清理过期文件失败:', error)
    return 0
  }
}
