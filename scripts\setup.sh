#!/bin/bash

# 项目初始化脚本
echo "🚀 开始项目初始化..."
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 解析命令行参数
SKIP_CONFIG=false
SILENT=false

while [[ $# -gt 0 ]]; do
  case $1 in
    --skip-config)
      SKIP_CONFIG=true
      shift
      ;;
    --silent)
      SILENT=true
      shift
      ;;
    -h|--help)
      echo "用法: $0 [选项]"
      echo "选项:"
      echo "  --skip-config    跳过数据库和Redis配置"
      echo "  --silent         静默模式，使用默认配置"
      echo "  -h, --help       显示帮助信息"
      exit 0
      ;;
    *)
      echo "未知选项: $1"
      echo "使用 $0 --help 查看帮助"
      exit 1
      ;;
  esac
done

# 检查函数
check_command() {
    if command -v "$1" &> /dev/null; then
        echo -e "${GREEN}✅ $1 已安装${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 未安装${NC}"
        return 1
    fi
}

# 配置数据库连接
configure_database() {
    echo -e "${BLUE}🗄️  配置数据库连接...${NC}"
    echo ""

    # 默认值
    local db_host="localhost"
    local db_port="5432"
    local db_user="postgres"
    local db_password=""
    local db_name="shop_db"

    if [ "$SILENT" = false ]; then
        echo "请输入数据库连接信息（按回车使用默认值）："

        read -p "数据库主机 [$db_host]: " input_host
        db_host=${input_host:-$db_host}

        read -p "数据库端口 [$db_port]: " input_port
        db_port=${input_port:-$db_port}

        read -p "数据库用户名 [$db_user]: " input_user
        db_user=${input_user:-$db_user}

        read -s -p "数据库密码: " input_password
        echo ""
        db_password=${input_password:-$db_password}

        read -p "数据库名称 [$db_name]: " input_name
        db_name=${input_name:-$db_name}
    fi

    # 构建数据库URL
    local database_url="postgresql://${db_user}"
    if [ -n "$db_password" ]; then
        database_url="${database_url}:${db_password}"
    fi
    database_url="${database_url}@${db_host}:${db_port}/${db_name}"

    # 更新.env文件
    if [ -f ".env" ]; then
        # 使用sed更新现有配置
        sed -i.bak "s|^DATABASE_URL=.*|DATABASE_URL=\"${database_url}\"|" .env
        sed -i.bak "s|^DB_HOST=.*|DB_HOST=${db_host}|" .env
        sed -i.bak "s|^DB_PORT=.*|DB_PORT=${db_port}|" .env
        sed -i.bak "s|^DB_USER=.*|DB_USER=${db_user}|" .env
        sed -i.bak "s|^DB_NAME=.*|DB_NAME=${db_name}|" .env

        if [ -n "$db_password" ]; then
            sed -i.bak "s|^DB_PASSWORD=.*|DB_PASSWORD=${db_password}|" .env
        fi

        # 删除备份文件
        rm -f .env.bak

        echo -e "${GREEN}✅ 数据库配置已更新${NC}"
    else
        echo -e "${RED}❌ .env 文件不存在${NC}"
        return 1
    fi
}

# 配置Redis连接
configure_redis() {
    echo -e "${BLUE}📦 配置Redis连接...${NC}"
    echo ""

    # 默认值
    local redis_host="localhost"
    local redis_port="6379"
    local redis_password=""
    local redis_db="0"

    if [ "$SILENT" = false ]; then
        echo "请输入Redis连接信息（按回车使用默认值）："

        read -p "Redis主机 [$redis_host]: " input_host
        redis_host=${input_host:-$redis_host}

        read -p "Redis端口 [$redis_port]: " input_port
        redis_port=${input_port:-$redis_port}

        read -s -p "Redis密码（可选）: " input_password
        echo ""
        redis_password=${input_password:-$redis_password}

        read -p "Redis数据库编号 [$redis_db]: " input_db
        redis_db=${input_db:-$redis_db}
    fi

    # 构建Redis URL
    local redis_url="redis://"
    if [ -n "$redis_password" ]; then
        redis_url="${redis_url}:${redis_password}@"
    fi
    redis_url="${redis_url}${redis_host}:${redis_port}/${redis_db}"

    # 更新.env文件
    if [ -f ".env" ]; then
        sed -i.bak "s|^REDIS_URL=.*|REDIS_URL=${redis_url}|" .env
        rm -f .env.bak
        echo -e "${GREEN}✅ Redis配置已更新${NC}"
    else
        echo -e "${RED}❌ .env 文件不存在${NC}"
        return 1
    fi
}

# 检查必要的工具
echo -e "${BLUE}📋 检查必要工具...${NC}"
MISSING_TOOLS=0

if ! check_command "node"; then
    echo -e "${YELLOW}请先安装 Node.js: https://nodejs.org/${NC}"
    MISSING_TOOLS=1
fi

if ! check_command "npm"; then
    echo -e "${YELLOW}请先安装 npm (通常随 Node.js 一起安装)${NC}"
    MISSING_TOOLS=1
fi

if ! check_command "git"; then
    echo -e "${YELLOW}请先安装 Git: https://git-scm.com/${NC}"
    MISSING_TOOLS=1
fi

if [ $MISSING_TOOLS -eq 1 ]; then
    echo -e "${RED}❌ 缺少必要工具，请安装后重新运行${NC}"
    exit 1
fi

echo ""

# 检查是否需要安装依赖
if [ ! -d "node_modules" ]; then
    echo -e "${BLUE}📦 安装项目依赖...${NC}"
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 依赖安装失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
else
    echo -e "${GREEN}✅ 项目依赖已存在${NC}"
fi

echo ""

# 创建上传目录
echo -e "${BLUE}📁 创建上传目录...${NC}"
mkdir -p public/uploads/images
mkdir -p public/uploads/avatars
mkdir -p public/uploads/documents

# 设置目录权限
chmod 755 public/uploads
chmod 755 public/uploads/images
chmod 755 public/uploads/avatars
chmod 755 public/uploads/documents

# 创建.gitkeep文件
touch public/uploads/images/.gitkeep
touch public/uploads/avatars/.gitkeep
touch public/uploads/documents/.gitkeep

echo -e "${GREEN}✅ 上传目录创建完成${NC}"

echo ""

# 检查环境变量文件
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        echo -e "${BLUE}📝 创建环境变量文件...${NC}"
        cp .env.example .env
        echo -e "${GREEN}✅ 环境变量文件已创建${NC}"
    else
        echo -e "${YELLOW}⚠️  未找到 .env.example 文件，请手动创建 .env 文件${NC}"
    fi
else
    echo -e "${GREEN}✅ 环境变量文件已存在${NC}"
fi

echo ""

# 询问是否需要配置数据库和Redis
if [ "$SKIP_CONFIG" = false ] && [ "$SILENT" = false ]; then
    echo -e "${BLUE}🔧 数据库和Redis配置${NC}"
    echo ""

    read -p "是否需要配置数据库连接？(y/N): " configure_db
    if [[ $configure_db =~ ^[Yy]$ ]]; then
        configure_database
        echo ""
    fi

    read -p "是否需要配置Redis连接？(y/N): " configure_redis_prompt
    if [[ $configure_redis_prompt =~ ^[Yy]$ ]]; then
        configure_redis
        echo ""
    fi

    if [[ ! $configure_db =~ ^[Yy]$ ]] && [[ ! $configure_redis_prompt =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}⚠️  跳过配置，请手动编辑 .env 文件${NC}"
        echo ""
    fi
elif [ "$SKIP_CONFIG" = true ]; then
    echo -e "${YELLOW}⚠️  跳过数据库和Redis配置（使用 --skip-config 参数）${NC}"
    echo ""
elif [ "$SILENT" = true ]; then
    echo -e "${BLUE}🔧 使用默认配置（静默模式）${NC}"
    configure_database
    configure_redis
    echo ""
fi

# 数据库初始化
echo -e "${BLUE}🗄️  初始化数据库...${NC}"

# 检查是否有Prisma配置
if [ ! -f "prisma/schema.prisma" ]; then
    echo -e "${RED}❌ 未找到 Prisma 配置文件${NC}"
    exit 1
fi

# 生成 Prisma Client
echo -e "${BLUE}📦 生成 Prisma Client...${NC}"
npx prisma generate
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 生成 Prisma Client 失败${NC}"
    exit 1
fi

# 验证配置
validate_config() {
    echo -e "${BLUE}🔍 验证配置...${NC}"

    if [ ! -f ".env" ]; then
        echo -e "${RED}❌ .env 文件不存在${NC}"
        return 1
    fi

    # 检查必要的配置项
    local required_vars=("DATABASE_URL" "JWT_SECRET")
    local missing_vars=()

    for var in "${required_vars[@]}"; do
        if ! grep -q "^${var}=" .env; then
            missing_vars+=("$var")
        fi
    done

    if [ ${#missing_vars[@]} -gt 0 ]; then
        echo -e "${YELLOW}⚠️  缺少必要的配置项: ${missing_vars[*]}${NC}"
        echo -e "${YELLOW}   请检查 .env 文件${NC}"
        return 1
    fi

    echo -e "${GREEN}✅ 配置验证通过${NC}"
    return 0
}

# 测试数据库连接
test_database_connection() {
    echo -e "${BLUE}🔗 测试数据库连接...${NC}"

    # 尝试连接数据库
    if npx prisma db push --accept-data-loss > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 数据库连接成功${NC}"
        return 0
    else
        echo -e "${RED}❌ 数据库连接失败${NC}"

        # 提供详细的诊断信息
        echo -e "${BLUE}🔍 诊断信息：${NC}"

        # 检查数据库URL格式
        local db_url=$(grep "^DATABASE_URL=" .env | cut -d'=' -f2- | tr -d '"')
        if [[ $db_url =~ ^postgresql://([^:]+):([^@]*)@([^:]+):([0-9]+)/(.+)$ ]]; then
            local db_user="${BASH_REMATCH[1]}"
            local db_host="${BASH_REMATCH[3]}"
            local db_port="${BASH_REMATCH[4]}"
            local db_name="${BASH_REMATCH[5]}"

            echo -e "  数据库主机: ${db_host}"
            echo -e "  数据库端口: ${db_port}"
            echo -e "  数据库用户: ${db_user}"
            echo -e "  数据库名称: ${db_name}"

            # 测试主机连接
            if command -v nc &> /dev/null; then
                if nc -z "$db_host" "$db_port" 2>/dev/null; then
                    echo -e "  ${GREEN}✅ 主机端口可达${NC}"
                else
                    echo -e "  ${RED}❌ 主机端口不可达${NC}"
                fi
            elif command -v telnet &> /dev/null; then
                if timeout 3 telnet "$db_host" "$db_port" 2>/dev/null | grep -q "Connected"; then
                    echo -e "  ${GREEN}✅ 主机端口可达${NC}"
                else
                    echo -e "  ${RED}❌ 主机端口不可达${NC}"
                fi
            fi
        else
            echo -e "  ${RED}❌ 数据库URL格式错误${NC}"
        fi

        return 1
    fi
}

# 检查数据库连接
echo -e "${BLUE}🔗 检查数据库连接...${NC}"

# 验证配置
if ! validate_config; then
    echo -e "${YELLOW}⚠️  配置验证失败，跳过数据库初始化${NC}"
    echo -e "${YELLOW}   请手动配置 .env 文件后运行: npm run db:init${NC}"
    echo ""
    echo -e "${GREEN}🎉 项目基础初始化完成！${NC}"
    exit 0
fi

# 数据库连接和初始化循环
database_init_success=false
max_retries=3
retry_count=0

while [ "$database_init_success" = false ] && [ $retry_count -lt $max_retries ]; do
    if test_database_connection; then

        # 推送数据库架构
        echo -e "${BLUE}🗄️  推送数据库架构...${NC}"
        npx prisma db push
        if [ $? -ne 0 ]; then
            echo -e "${RED}❌ 推送数据库架构失败${NC}"
            exit 1
        fi

        # 填充种子数据
        echo -e "${BLUE}🌱 填充种子数据...${NC}"
        npx prisma db seed
        if [ $? -ne 0 ]; then
            echo -e "${YELLOW}⚠️  种子数据填充失败，可能是数据已存在${NC}"
        else
            echo -e "${GREEN}✅ 种子数据填充完成${NC}"
        fi

        database_init_success=true
    else
        retry_count=$((retry_count + 1))

        if [ $retry_count -lt $max_retries ]; then
            if [ "$SILENT" = false ]; then
                echo ""
                echo -e "${YELLOW}可能的原因：${NC}"
                echo "1. 数据库服务未启动"
                echo "2. 数据库连接信息错误"
                echo "3. 数据库用户权限不足"
                echo "4. 网络连接问题"
                echo ""

                read -p "是否重新配置数据库连接？(y/N): " reconfigure_db
                if [[ $reconfigure_db =~ ^[Yy]$ ]]; then
                    echo ""
                    configure_database
                    echo ""
                    echo -e "${BLUE}🔗 重新检查数据库连接...${NC}"
                else
                    echo -e "${YELLOW}⚠️  跳过数据库初始化${NC}"
                    break
                fi
            else
                echo -e "${YELLOW}⚠️  静默模式下跳过重试${NC}"
                break
            fi
        else
            echo -e "${RED}❌ 已达到最大重试次数 ($max_retries)${NC}"
            echo -e "${YELLOW}⚠️  数据库初始化失败，请检查配置后手动运行: npm run db:init${NC}"
            break
        fi
    fi
done

echo ""

# 完成提示
echo -e "${GREEN}🎉 项目初始化完成！${NC}"
echo ""
echo -e "${BLUE}📋 接下来的步骤：${NC}"
echo "1. 检查并配置 .env 文件中的环境变量"
echo "2. 确保数据库服务正在运行"
echo "3. 运行 npm run dev 启动开发服务器"
echo ""
echo -e "${BLUE}📋 测试账号信息：${NC}"
echo "管理员: <EMAIL> / 123456"
echo "商家: <EMAIL> / 123456"
echo "用户: <EMAIL> / 123456"
echo ""
echo -e "${BLUE}🛠️  常用命令：${NC}"
echo "npm run dev          # 启动开发服务器"
echo "npm run db:studio    # 打开数据库管理界面"
echo "npm run db:reset     # 重置数据库"
echo "npm run build        # 构建生产版本"
echo ""
echo -e "${GREEN}🌐 现在可以运行 npm run dev 启动应用！${NC}"
