# 社交购物网站

基于 Nuxt.js 3 开发的现代化社交购物平台，集购物与社交功能于一体。

## 🚀 功能特性

### 用户系统

- ✅ 用户注册/登录
- ✅ JWT身份认证
- ✅ 个人资料管理
- ✅ 权限控制（用户/商家/管理员）
- ✅ 账户安全功能

### 商品系统

- 🔄 商品展示和搜索
- 🔄 商品分类管理
- 🔄 购物车功能
- 🔄 商品收藏和评价
- 🔄 商家后台管理

### 订单系统

- 🔄 订单创建和管理
- 🔄 多种支付方式
- 🔄 订单状态跟踪
- 🔄 售后服务
- 🔄 物流管理

### 社交功能

- 🔄 用户关注系统
- 🔄 私信和消息通知
- 🔄 动态发布和分享
- 🔄 点赞、评论、转发
- 🔄 话题讨论

## 🛠️ 技术栈

- **前端框架**: Nuxt.js 3 + Vue.js 3 + TypeScript
- **UI组件**: Nuxt UI + Tailwind CSS
- **状态管理**: Pinia
- **数据库**: PostgreSQL + Prisma ORM
- **缓存**: Redis
- **身份认证**: JWT
- **文件上传**: 本地存储/云存储
- **支付集成**: 支付宝/微信支付

## 📦 安装和运行

### 环境要求

- Node.js >= 18.0.0
- PostgreSQL >= 13.0
- Redis >= 6.0
- Docker & Docker Compose (可选，用于本地开发)

### 🚀 一键设置开发环境

```bash
# Linux/macOS
npm run dev:setup

# Windows
npm run dev:setup:win
```

### 手动安装

## 🚀 快速开始

> 📖 **详细指南**: 查看 [快速开始指南](docs/QUICK_START.md) 获取完整的安装和配置说明

### 一键初始化（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd shop

# 一键初始化项目（交互式配置）
npm run setup        # Linux/macOS
npm run setup:win    # Windows

# 跳过数据库配置（使用默认配置）
npm run setup:skip        # Linux/macOS
npm run setup:skip:win    # Windows

# 静默模式（自动使用默认配置）
npm run setup:silent        # Linux/macOS
npm run setup:silent:win    # Windows

# 启动开发服务器
npm run dev
```

**初始化脚本会自动完成：**

- ✅ 检查必要工具并安装依赖
- ✅ 创建上传目录和环境配置
- ✅ 交互式配置数据库和Redis连接
- ✅ 初始化数据库和填充测试数据

**命令选项说明：**

| 命令                   | 说明                                 | 适用场景                 |
| ---------------------- | ------------------------------------ | ------------------------ |
| `npm run setup`        | 交互式初始化，询问数据库配置         | 首次安装，需要自定义配置 |
| `npm run setup:skip`   | 跳过配置询问，使用.env.example默认值 | 快速搭建，后续手动配置   |
| `npm run setup:silent` | 静默模式，自动使用默认配置           | 自动化部署，CI/CD环境    |

### 手动安装步骤

#### 1. 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install

# 或使用 pnpm
pnpm install
```

#### 2. 启动开发服务 (使用Docker)

```bash
# 启动PostgreSQL和Redis
npm run dev:services

# 查看服务状态
docker-compose -f docker-compose.dev.yml ps
```

#### 3. 环境配置

```bash
# 复制环境变量文件
cp .env.example .env
```

修改 `.env` 文件中的配置

```env
# PostgreSQL数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=social_shop
DATABASE_URL=postgresql://postgres:your_password@localhost:5432/social_shop

# Redis配置
REDIS_URL=redis://localhost:6379

# JWT密钥
JWT_SECRET=your-super-secret-jwt-key-here
```

#### 4. 项目初始化

```bash
# 一键初始化项目（推荐）
npm run setup        # Linux/macOS
npm run setup:win    # Windows
```

**初始化脚本会自动完成：**

- ✅ 检查必要工具（Node.js, npm, Git）
- ✅ 安装项目依赖
- ✅ 创建上传目录结构
- ✅ 复制环境变量配置文件
- ✅ 生成Prisma客户端
- ✅ 初始化数据库架构
- ✅ 填充测试数据

**手动初始化步骤（可选）：**

```bash
npm install            # 安装依赖
npm run db:generate    # 生成Prisma客户端
npm run db:init        # 初始化数据库
npm run uploads:init   # 创建上传目录
```

**项目包含以下功能模块：**

- ✅ 用户系统（注册、登录、个人信息）
- ✅ 商品管理（分类、商品、库存）
- ✅ 购物车和订单系统
- ✅ 优惠券系统（同享券、互斥券）
- ✅ 收藏和评价功能
- ✅ 社交功能（动态、关注、私信）
- ✅ 文件上传系统（图片、头像）
- ✅ 支付和物流系统

#### 5. 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 🛠️ 开发工具

项目配置了完整的开发工具链：

- **代码检查**: ESLint + Prettier
- **类型检查**: TypeScript
- **Git Hooks**: 自动代码格式化和检查
- **调试支持**: VSCode调试配置
- **数据库管理**: Prisma Studio
- **开发服务**: Docker Compose

### 📋 开发命令

```bash
# 代码质量
npm run lint              # 代码检查
npm run lint:fix          # 自动修复
npm run format            # 代码格式化
npm run type-check        # 类型检查

# 项目初始化
npm run setup             # 交互式初始化（Linux/macOS）
npm run setup:win         # 交互式初始化（Windows）
npm run setup:skip        # 跳过配置询问（Linux/macOS）
npm run setup:skip:win    # 跳过配置询问（Windows）
npm run setup:silent      # 静默模式初始化（Linux/macOS）
npm run setup:silent:win  # 静默模式初始化（Windows）

# 数据库操作
npm run db:studio         # 打开Prisma Studio
npm run db:migrate        # 运行迁移
npm run db:seed           # 填充数据
npm run db:reset          # 重置数据库
npm run db:init           # 单独初始化数据库
npm run db:test           # 测试数据库连接（Linux/macOS）
npm run db:test:win       # 测试数据库连接（Windows）
npm run uploads:init      # 单独创建上传目录

# Docker服务
npm run dev:services      # 启动开发服务
npm run dev:services:stop # 停止开发服务
npm run dev:clean         # 清理缓存
```

## 🏗️ 项目结构

```
social-shop/
├── assets/              # 静态资源
│   └── css/            # 样式文件
├── components/          # Vue组件
│   ├── User/           # 用户相关组件
│   ├── Product/        # 商品相关组件
│   ├── Order/          # 订单相关组件
│   ├── Social/         # 社交相关组件
│   └── Common/         # 公共组件
├── layouts/            # 布局组件
├── middleware/         # 中间件
├── pages/              # 页面路由
├── plugins/            # 插件
├── server/             # 服务端代码
│   ├── api/            # API路由
│   └── middleware/     # 服务端中间件
├── stores/             # Pinia状态管理
├── types/              # TypeScript类型定义
├── utils/              # 工具函数
├── nuxt.config.ts      # Nuxt配置
└── package.json        # 项目配置
```

## 🔧 开发指南

### 代码规范

- 使用 ESLint 进行代码检查
- 使用 Prettier 进行代码格式化
- 遵循 Vue.js 3 Composition API 规范
- 使用 TypeScript 进行类型检查

### 提交规范

使用 Conventional Commits 规范：

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### API设计

- 遵循 RESTful API 设计原则
- 使用统一的响应格式
- 实现适当的错误处理
- 添加请求验证和安全检查

## 📝 API文档

### 认证接口

- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `POST /api/auth/refresh` - 刷新token
- `GET /api/auth/check-username` - 检查用户名是否可用
- `GET /api/auth/check-email` - 检查邮箱是否可用

### 用户接口

- `GET /api/users/profile` - 获取用户信息
- `PUT /api/users/profile` - 更新用户信息
- `GET /api/users/:id` - 获取指定用户信息
- `GET /api/users/orders` - 获取用户订单列表
- `GET /api/users/favorites` - 获取用户收藏列表

### 商品接口

- `GET /api/products` - 商品列表
- `GET /api/products/:id` - 商品详情
- `GET /api/products/search` - 商品搜索
- `GET /api/search/suggestions` - 获取搜索建议
- `GET /api/categories` - 商品分类
- `GET /api/products/:id/reviews` - 获取商品评价列表
- `POST /api/products/:id/reviews` - 提交商品评价
- `GET /api/products/:id/coupons` - 获取商品可用优惠券

### 优惠券接口

- `POST /api/coupons/:id/claim` - 领取优惠券
- `GET /api/users/coupons` - 获取用户可用优惠券
- `POST /api/coupons/calculate` - 计算优惠券优惠金额

### 文件上传接口

- `POST /api/upload/images` - 图片上传（支持多文件）
- `POST /api/upload/avatar` - 头像上传（单文件）

## 🚀 部署

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

### 部署到服务器

1. 构建应用
2. 上传到服务器
3. 配置环境变量
4. 启动应用

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📚 详细文档

- [📖 快速开始指南](docs/QUICK_START.md) - 项目安装和配置
- [🔧 配置指南](docs/CONFIGURATION.md) - 详细的配置说明
- [🚀 开发指南](docs/DEVELOPMENT.md) - 开发环境搭建
- [📝 API文档](docs/API.md) - 完整的API接口文档
- [📊 日志API文档](docs/API_LOGS.md) - 日志管理API详细说明
- [📋 日志系统文档](docs/LOGGING.md) - 日志系统使用指南

## 📞 联系我们

- 项目地址: [GitHub](https://github.com/your-username/social-shop)
- 问题反馈: [Issues](https://github.com/your-username/social-shop/issues)
- 邮箱: <EMAIL>

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！
