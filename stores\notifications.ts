import type { Notification, PaginatedResponse } from '~/types'

/**
 * 通知系统状态管理
 */
export const useNotificationsStore = defineStore('notifications', () => {
  // 状态
  const notifications = ref<Notification[]>([])
  const unreadCount = ref(0)
  const isLoading = ref(false)
  const pagination = ref({
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0
  })

  // API实例
  const request = createAuthenticatedRequest()
  const authStore = useAuthStore()
  const toast = useToast()

  // 获取通知列表
  const fetchNotifications = async (params: Record<string, any> = {}) => {
    if (!authStore.isLoggedIn) return

    try {
      isLoading.value = true
      
      const response = await request.get<PaginatedResponse<Notification>>('/api/notifications', {
        params: {
          page: pagination.value.page,
          pageSize: pagination.value.pageSize,
          ...params
        }
      })
      
      notifications.value = response.items
      pagination.value = {
        page: response.page,
        pageSize: response.pageSize,
        total: response.total,
        totalPages: response.totalPages
      }
    } catch (error: any) {
      console.error('获取通知列表失败:', error)
      if (error.statusCode !== 401) {
        toast.add({
          title: '获取通知失败',
          description: error.message || '请稍后重试',
          color: 'red'
        })
      }
    } finally {
      isLoading.value = false
    }
  }

  // 获取未读通知数量
  const fetchUnreadCount = async () => {
    if (!authStore.isLoggedIn) {
      unreadCount.value = 0
      return
    }

    try {
      const count = await request.get<number>('/api/notifications/unread-count')
      unreadCount.value = count
    } catch (error: any) {
      console.error('获取未读通知数量失败:', error)
    }
  }

  // 标记通知为已读
  const markAsRead = async (notificationId: number) => {
    if (!authStore.isLoggedIn) return

    try {
      await request.patch(`/api/notifications/${notificationId}/read`)
      
      // 更新本地状态
      const notification = notifications.value.find(n => n.id === notificationId)
      if (notification && !notification.isRead) {
        notification.isRead = true
        unreadCount.value = Math.max(0, unreadCount.value - 1)
      }
    } catch (error: any) {
      console.error('标记通知已读失败:', error)
    }
  }

  // 标记所有通知为已读
  const markAllAsRead = async () => {
    if (!authStore.isLoggedIn) return

    try {
      await request.patch('/api/notifications/read-all')
      
      // 更新本地状态
      notifications.value.forEach(notification => {
        notification.isRead = true
      })
      unreadCount.value = 0
      
      toast.add({
        title: '已标记所有通知为已读',
        color: 'green'
      })
    } catch (error: any) {
      console.error('标记所有通知已读失败:', error)
      toast.add({
        title: '操作失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
    }
  }

  // 删除通知
  const deleteNotification = async (notificationId: number) => {
    if (!authStore.isLoggedIn) return

    try {
      await request.delete(`/api/notifications/${notificationId}`)
      
      // 从本地列表中移除
      const index = notifications.value.findIndex(n => n.id === notificationId)
      if (index !== -1) {
        const notification = notifications.value[index]
        if (!notification.isRead) {
          unreadCount.value = Math.max(0, unreadCount.value - 1)
        }
        notifications.value.splice(index, 1)
      }
      
      toast.add({
        title: '通知已删除',
        color: 'blue'
      })
    } catch (error: any) {
      console.error('删除通知失败:', error)
      toast.add({
        title: '删除失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
    }
  }

  // 清空所有通知
  const clearAllNotifications = async () => {
    if (!authStore.isLoggedIn) return

    try {
      await request.delete('/api/notifications/clear-all')
      
      notifications.value = []
      unreadCount.value = 0
      
      toast.add({
        title: '所有通知已清空',
        color: 'blue'
      })
    } catch (error: any) {
      console.error('清空通知失败:', error)
      toast.add({
        title: '清空失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
    }
  }

  // 创建通知（用于实时通知）
  const addNotification = (notification: Notification) => {
    notifications.value.unshift(notification)
    if (!notification.isRead) {
      unreadCount.value += 1
    }
    
    // 显示浏览器通知
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.content,
        icon: '/favicon.ico'
      })
    }
  }

  // 请求浏览器通知权限
  const requestNotificationPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      return permission === 'granted'
    }
    return false
  }

  // 获取通知类型图标
  const getNotificationIcon = (type: string) => {
    const icons = {
      'LIKE': 'heroicons:heart',
      'COMMENT': 'heroicons:chat-bubble-left',
      'FOLLOW': 'heroicons:user-plus',
      'ORDER': 'heroicons:shopping-bag',
      'PAYMENT': 'heroicons:credit-card',
      'SYSTEM': 'heroicons:bell',
      'PROMOTION': 'heroicons:gift'
    }
    return icons[type as keyof typeof icons] || 'heroicons:bell'
  }

  // 获取通知类型颜色
  const getNotificationColor = (type: string) => {
    const colors = {
      'LIKE': 'red',
      'COMMENT': 'blue',
      'FOLLOW': 'green',
      'ORDER': 'purple',
      'PAYMENT': 'yellow',
      'SYSTEM': 'gray',
      'PROMOTION': 'orange'
    }
    return colors[type as keyof typeof colors] || 'gray'
  }

  // 格式化通知时间
  const formatNotificationTime = (date: string | Date) => {
    const now = new Date()
    const notificationDate = new Date(date)
    const diff = now.getTime() - notificationDate.getTime()
    
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    
    if (minutes < 1) return '刚刚'
    if (minutes < 60) return `${minutes}分钟前`
    if (hours < 24) return `${hours}小时前`
    if (days < 7) return `${days}天前`
    
    return notificationDate.toLocaleDateString('zh-CN')
  }

  // 处理通知点击
  const handleNotificationClick = async (notification: Notification) => {
    // 标记为已读
    if (!notification.isRead) {
      await markAsRead(notification.id)
    }
    
    // 根据通知类型跳转到相应页面
    if (notification.actionUrl) {
      await navigateTo(notification.actionUrl)
    }
  }

  // 计算属性
  const hasUnread = computed(() => unreadCount.value > 0)
  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.isRead)
  )

  // 监听登录状态变化
  watch(() => authStore.isLoggedIn, (isLoggedIn) => {
    if (isLoggedIn) {
      fetchUnreadCount()
    } else {
      notifications.value = []
      unreadCount.value = 0
    }
  }, { immediate: true })

  // 定期刷新未读通知数量
  let refreshTimer: NodeJS.Timeout | null = null
  
  const startRefreshTimer = () => {
    if (refreshTimer) clearInterval(refreshTimer)
    
    refreshTimer = setInterval(() => {
      if (authStore.isLoggedIn) {
        fetchUnreadCount()
      }
    }, 30000) // 每30秒刷新一次
  }

  const stopRefreshTimer = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }

  // 清空状态
  const clearState = () => {
    notifications.value = []
    unreadCount.value = 0
    pagination.value = {
      page: 1,
      pageSize: 20,
      total: 0,
      totalPages: 0
    }
    stopRefreshTimer()
  }

  // 组件挂载时启动定时器
  onMounted(() => {
    startRefreshTimer()
  })

  // 组件卸载时清理定时器
  onUnmounted(() => {
    stopRefreshTimer()
  })

  return {
    // 状态
    notifications: readonly(notifications),
    unreadCount: readonly(unreadCount),
    isLoading: readonly(isLoading),
    pagination: readonly(pagination),
    
    // 方法
    fetchNotifications,
    fetchUnreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAllNotifications,
    addNotification,
    requestNotificationPermission,
    handleNotificationClick,
    
    // 工具方法
    getNotificationIcon,
    getNotificationColor,
    formatNotificationTime,
    
    // 计算属性
    hasUnread,
    unreadNotifications,
    
    // 定时器控制
    startRefreshTimer,
    stopRefreshTimer,
    clearState
  }
})
