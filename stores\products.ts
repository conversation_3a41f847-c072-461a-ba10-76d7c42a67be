import type { Product, ProductSearchParams, PaginatedResponse } from '~/types'

/**
 * 商品状态管理
 */
export const useProductsStore = defineStore('products', () => {
  // 状态
  const products = ref<Product[]>([])
  const currentProduct = ref<Product | null>(null)
  const favorites = ref<Set<number>>(new Set())
  const cartItems = ref<Map<number, number>>(new Map())
  const isLoading = ref(false)
  const searchParams = ref<ProductSearchParams>({})
  const pagination = ref({
    page: 1,
    pageSize: 12,
    total: 0,
    totalPages: 0
  })

  // API实例
  const request = createAuthenticatedRequest()
  const authStore = useAuthStore()
  const toast = useToast()

  // 获取商品列表
  const fetchProducts = async (params: ProductSearchParams = {}) => {
    try {
      isLoading.value = true
      searchParams.value = { ...searchParams.value, ...params }
      
      const response = await request.get<PaginatedResponse<Product>>('/api/products', {
        params: searchParams.value,
        requireAuth: false
      })
      
      products.value = response.items
      pagination.value = {
        page: response.page,
        pageSize: response.pageSize,
        total: response.total,
        totalPages: response.totalPages
      }
      
      // 如果用户已登录，获取收藏和购物车状态
      if (authStore.isLoggedIn) {
        await Promise.all([
          fetchFavoriteStatus(),
          fetchCartStatus()
        ])
      }
    } catch (error: any) {
      console.error('获取商品列表失败:', error)
      toast.add({
        title: '获取商品失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
    } finally {
      isLoading.value = false
    }
  }

  // 获取商品详情
  const fetchProduct = async (id: number) => {
    try {
      isLoading.value = true
      
      const product = await request.get<Product>(`/api/products/${id}`, {
        requireAuth: false
      })
      
      currentProduct.value = product
      
      // 如果用户已登录，获取收藏和购物车状态
      if (authStore.isLoggedIn) {
        await Promise.all([
          checkFavoriteStatus(id),
          checkCartStatus(id)
        ])
      }
      
      return product
    } catch (error: any) {
      console.error('获取商品详情失败:', error)
      toast.add({
        title: '获取商品详情失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 搜索商品
  const searchProducts = async (keyword: string, filters: Record<string, any> = {}) => {
    const params: ProductSearchParams = {
      keyword,
      ...filters,
      page: 1
    }
    
    await fetchProducts(params)
  }

  // 获取收藏状态
  const fetchFavoriteStatus = async () => {
    if (!authStore.isLoggedIn) return
    
    try {
      const favoriteIds = await request.get<number[]>('/api/favorites/products')
      favorites.value = new Set(favoriteIds)
    } catch (error) {
      console.error('获取收藏状态失败:', error)
    }
  }

  // 检查单个商品收藏状态
  const checkFavoriteStatus = async (productId: number) => {
    if (!authStore.isLoggedIn) return false
    
    try {
      const isFavorited = await request.get<boolean>(`/api/favorites/products/${productId}`)
      if (isFavorited) {
        favorites.value.add(productId)
      } else {
        favorites.value.delete(productId)
      }
      return isFavorited
    } catch (error) {
      console.error('检查收藏状态失败:', error)
      return false
    }
  }

  // 切换收藏状态
  const toggleFavorite = async (productId: number) => {
    if (!authStore.isLoggedIn) {
      toast.add({
        title: '请先登录',
        description: '登录后即可收藏商品',
        color: 'yellow'
      })
      return navigateTo('/login')
    }

    try {
      const isFavorited = favorites.value.has(productId)
      
      if (isFavorited) {
        await request.delete(`/api/favorites/products/${productId}`)
        favorites.value.delete(productId)
        toast.add({
          title: '已取消收藏',
          color: 'blue'
        })
      } else {
        await request.post(`/api/favorites/products/${productId}`)
        favorites.value.add(productId)
        toast.add({
          title: '收藏成功',
          color: 'green'
        })
      }
    } catch (error: any) {
      console.error('切换收藏状态失败:', error)
      toast.add({
        title: '操作失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
    }
  }

  // 获取购物车状态
  const fetchCartStatus = async () => {
    if (!authStore.isLoggedIn) return
    
    try {
      const cartData = await request.get<{ items: Array<{ productId: number, quantity: number }> }>('/api/cart')
      cartItems.value.clear()
      cartData.items.forEach(item => {
        cartItems.value.set(item.productId, item.quantity)
      })
    } catch (error) {
      console.error('获取购物车状态失败:', error)
    }
  }

  // 检查单个商品购物车状态
  const checkCartStatus = async (productId: number) => {
    if (!authStore.isLoggedIn) return 0
    
    return cartItems.value.get(productId) || 0
  }

  // 添加到购物车
  const addToCart = async (productId: number, quantity: number = 1) => {
    if (!authStore.isLoggedIn) {
      toast.add({
        title: '请先登录',
        description: '登录后即可添加到购物车',
        color: 'yellow'
      })
      return navigateTo('/login')
    }

    try {
      await request.post('/api/cart', { productId, quantity })
      
      const currentQuantity = cartItems.value.get(productId) || 0
      cartItems.value.set(productId, currentQuantity + quantity)
      
      toast.add({
        title: '添加成功',
        description: '商品已添加到购物车',
        color: 'green'
      })
    } catch (error: any) {
      console.error('添加到购物车失败:', error)
      toast.add({
        title: '添加失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
    }
  }

  // 立即购买
  const buyNow = async (productId: number, quantity: number = 1) => {
    if (!authStore.isLoggedIn) {
      toast.add({
        title: '请先登录',
        description: '登录后即可购买商品',
        color: 'yellow'
      })
      return navigateTo('/login')
    }

    try {
      // 跳转到订单确认页面
      await navigateTo({
        path: '/checkout',
        query: {
          type: 'buy-now',
          productId,
          quantity
        }
      })
    } catch (error) {
      console.error('立即购买失败:', error)
    }
  }

  // 分享商品
  const shareProduct = async (productId: number, content: string = '') => {
    if (!authStore.isLoggedIn) {
      toast.add({
        title: '请先登录',
        description: '登录后即可分享商品',
        color: 'yellow'
      })
      return navigateTo('/login')
    }

    try {
      await request.post('/api/posts', {
        content: content || `推荐一个不错的商品给大家！`,
        type: 'PRODUCT_SHARE',
        productId
      })
      
      toast.add({
        title: '分享成功',
        description: '商品已分享到动态',
        color: 'green'
      })
    } catch (error: any) {
      console.error('分享商品失败:', error)
      toast.add({
        title: '分享失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
    }
  }

  // 计算属性
  const isFavorited = (productId: number) => favorites.value.has(productId)
  const getCartQuantity = (productId: number) => cartItems.value.get(productId) || 0
  const isInCart = (productId: number) => cartItems.value.has(productId)

  // 清空状态
  const clearState = () => {
    products.value = []
    currentProduct.value = null
    favorites.value.clear()
    cartItems.value.clear()
    searchParams.value = {}
    pagination.value = {
      page: 1,
      pageSize: 12,
      total: 0,
      totalPages: 0
    }
  }

  return {
    // 状态
    products: readonly(products),
    currentProduct: readonly(currentProduct),
    favorites: readonly(favorites),
    cartItems: readonly(cartItems),
    isLoading: readonly(isLoading),
    searchParams: readonly(searchParams),
    pagination: readonly(pagination),
    
    // 方法
    fetchProducts,
    fetchProduct,
    searchProducts,
    toggleFavorite,
    addToCart,
    buyNow,
    shareProduct,
    
    // 计算属性
    isFavorited,
    getCartQuantity,
    isInCart,
    
    // 工具方法
    clearState
  }
})
