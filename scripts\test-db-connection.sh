#!/bin/bash

# 数据库连接测试脚本
echo "🔗 数据库连接测试工具"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查.env文件
if [ ! -f ".env" ]; then
    echo -e "${RED}❌ .env 文件不存在${NC}"
    echo "请先运行 npm run setup 或手动创建 .env 文件"
    exit 1
fi

# 读取数据库配置
echo -e "${BLUE}📋 读取数据库配置...${NC}"
source .env

if [ -z "$DATABASE_URL" ]; then
    echo -e "${RED}❌ DATABASE_URL 未配置${NC}"
    exit 1
fi

echo -e "数据库URL: ${DATABASE_URL}"
echo ""

# 解析数据库URL
if [[ $DATABASE_URL =~ ^postgresql://([^:]+):([^@]*)@([^:]+):([0-9]+)/(.+)$ ]]; then
    DB_USER="${BASH_REMATCH[1]}"
    DB_PASSWORD="${BASH_REMATCH[2]}"
    DB_HOST="${BASH_REMATCH[3]}"
    DB_PORT="${BASH_REMATCH[4]}"
    DB_NAME="${BASH_REMATCH[5]}"
    
    echo -e "${BLUE}🔍 连接参数：${NC}"
    echo -e "  主机: ${DB_HOST}"
    echo -e "  端口: ${DB_PORT}"
    echo -e "  用户: ${DB_USER}"
    echo -e "  数据库: ${DB_NAME}"
    echo ""
else
    echo -e "${RED}❌ 数据库URL格式错误${NC}"
    echo "正确格式: postgresql://用户名:密码@主机:端口/数据库名"
    exit 1
fi

# 测试主机端口连通性
echo -e "${BLUE}🌐 测试网络连通性...${NC}"
if command -v nc &> /dev/null; then
    if nc -z "$DB_HOST" "$DB_PORT" 2>/dev/null; then
        echo -e "${GREEN}✅ 主机端口 ${DB_HOST}:${DB_PORT} 可达${NC}"
    else
        echo -e "${RED}❌ 主机端口 ${DB_HOST}:${DB_PORT} 不可达${NC}"
        echo -e "${YELLOW}请检查：${NC}"
        echo "1. PostgreSQL 服务是否启动"
        echo "2. 防火墙设置"
        echo "3. 网络连接"
        exit 1
    fi
elif command -v telnet &> /dev/null; then
    if timeout 3 telnet "$DB_HOST" "$DB_PORT" 2>/dev/null | grep -q "Connected"; then
        echo -e "${GREEN}✅ 主机端口 ${DB_HOST}:${DB_PORT} 可达${NC}"
    else
        echo -e "${RED}❌ 主机端口 ${DB_HOST}:${DB_PORT} 不可达${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠️  无法测试端口连通性（缺少 nc 或 telnet 工具）${NC}"
fi

echo ""

# 测试数据库连接
echo -e "${BLUE}🗄️  测试数据库连接...${NC}"

# 检查是否安装了psql
if command -v psql &> /dev/null; then
    echo "使用 psql 测试连接..."
    
    # 构建psql连接字符串
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT version();" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 数据库连接成功${NC}"
    else
        echo -e "${RED}❌ 数据库连接失败${NC}"
        echo -e "${YELLOW}可能的原因：${NC}"
        echo "1. 用户名或密码错误"
        echo "2. 数据库不存在"
        echo "3. 用户权限不足"
        
        # 尝试连接到默认数据库
        echo ""
        echo -e "${BLUE}🔄 尝试连接到 postgres 数据库...${NC}"
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "postgres" -c "SELECT 1;" 2>/dev/null
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ 可以连接到 postgres 数据库${NC}"
            echo -e "${YELLOW}建议：创建目标数据库 ${DB_NAME}${NC}"
            echo ""
            echo "创建数据库的SQL命令："
            echo "CREATE DATABASE ${DB_NAME};"
        else
            echo -e "${RED}❌ 无法连接到任何数据库${NC}"
        fi
        
        exit 1
    fi
else
    echo "使用 Prisma 测试连接..."
    
    # 使用Prisma测试连接
    if npx prisma db push --accept-data-loss > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Prisma 数据库连接成功${NC}"
    else
        echo -e "${RED}❌ Prisma 数据库连接失败${NC}"
        
        # 显示详细错误信息
        echo ""
        echo -e "${BLUE}📋 详细错误信息：${NC}"
        npx prisma db push --accept-data-loss 2>&1 | head -10
        
        exit 1
    fi
fi

echo ""

# 测试数据库权限
echo -e "${BLUE}🔐 测试数据库权限...${NC}"

if command -v psql &> /dev/null; then
    # 测试创建表权限
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        CREATE TABLE IF NOT EXISTS test_permissions (id SERIAL PRIMARY KEY);
        DROP TABLE IF EXISTS test_permissions;
    " 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 数据库权限正常${NC}"
    else
        echo -e "${RED}❌ 数据库权限不足${NC}"
        echo -e "${YELLOW}需要以下权限：${NC}"
        echo "- CREATE (创建表)"
        echo "- DROP (删除表)"
        echo "- INSERT (插入数据)"
        echo "- UPDATE (更新数据)"
        echo "- DELETE (删除数据)"
    fi
fi

echo ""
echo -e "${GREEN}🎉 数据库连接测试完成！${NC}"

# 提供后续建议
echo ""
echo -e "${BLUE}📋 后续步骤：${NC}"
echo "1. 如果连接成功，可以运行: npm run db:init"
echo "2. 如果连接失败，请检查上述错误信息"
echo "3. 需要帮助请查看: docs/CONFIGURATION.md"
