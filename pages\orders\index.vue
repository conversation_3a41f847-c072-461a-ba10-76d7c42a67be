<template>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold text-gray-900 mb-6">我的订单</h1>

    <!-- 订单状态筛选 -->
    <div class="mb-6">
      <UTabs :items="statusTabs" v-model="activeTab" @change="handleTabChange">
        <template #item="{ item }">
          <div class="py-4">
            <!-- 订单列表 -->
            <div v-if="ordersStore.isLoading" class="space-y-4">
              <div v-for="i in 3" :key="i" class="animate-pulse">
                <div class="bg-gray-200 h-32 rounded-lg"></div>
              </div>
            </div>

            <div v-else-if="filteredOrders.length === 0" class="text-center py-12">
              <Icon name="heroicons:document-text" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 class="text-lg font-semibold text-gray-600 mb-2">暂无订单</h3>
              <p class="text-gray-500 mb-6">您还没有{{ item.label }}的订单</p>
              <UButton @click="navigateTo('/products')">
                去购物
              </UButton>
            </div>

            <div v-else class="space-y-4">
              <UCard
                v-for="order in filteredOrders"
                :key="order.id"
                class="p-6"
              >
                <!-- 订单头部 -->
                <div class="flex items-center justify-between mb-4 pb-4 border-b">
                  <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-500">订单号：{{ order.orderNo }}</span>
                    <span class="text-sm text-gray-500">{{ formatDate(order.createdAt) }}</span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <UBadge :color="getOrderStatusColor(order.status)" variant="subtle">
                      {{ getOrderStatusText(order.status) }}
                    </UBadge>
                    <UBadge :color="getPaymentStatusColor(order.paymentStatus)" variant="subtle">
                      {{ getPaymentStatusText(order.paymentStatus) }}
                    </UBadge>
                  </div>
                </div>

                <!-- 订单商品 -->
                <div class="space-y-3 mb-4">
                  <div 
                    v-for="item in order.items"
                    :key="item.id"
                    class="flex items-center space-x-4"
                  >
                    <img 
                      :src="item.productImage || '/images/placeholder.jpg'"
                      :alt="item.productName"
                      class="w-16 h-16 object-cover rounded-lg"
                    >
                    <div class="flex-1">
                      <h4 class="font-medium text-gray-900">{{ item.productName }}</h4>
                      <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <span>¥{{ item.price }}</span>
                        <span>×{{ item.quantity }}</span>
                      </div>
                    </div>
                    <div class="text-right">
                      <div class="font-medium">¥{{ item.totalAmount }}</div>
                    </div>
                  </div>
                </div>

                <!-- 订单金额 -->
                <div class="flex justify-between items-center mb-4">
                  <div class="text-sm text-gray-500">
                    共{{ order.items.length }}件商品
                  </div>
                  <div class="text-right">
                    <div class="text-sm text-gray-500">
                      商品金额：¥{{ order.totalAmount }}
                    </div>
                    <div v-if="order.discountAmount > 0" class="text-sm text-green-600">
                      优惠金额：-¥{{ order.discountAmount }}
                    </div>
                    <div v-if="order.shippingAmount > 0" class="text-sm text-gray-500">
                      运费：¥{{ order.shippingAmount }}
                    </div>
                    <div class="text-lg font-bold text-red-600">
                      实付款：¥{{ order.paymentAmount }}
                    </div>
                  </div>
                </div>

                <!-- 订单操作 -->
                <div class="flex justify-end space-x-3">
                  <UButton
                    variant="outline"
                    size="sm"
                    @click="viewOrderDetail(order.id)"
                  >
                    查看详情
                  </UButton>

                  <!-- 根据订单状态显示不同操作 -->
                  <template v-if="order.status === 'PENDING'">
                    <UButton
                      variant="outline"
                      size="sm"
                      color="red"
                      @click="cancelOrder(order.id)"
                    >
                      取消订单
                    </UButton>
                    <UButton
                      v-if="order.paymentStatus === 'PENDING'"
                      size="sm"
                      @click="payOrder(order.id)"
                    >
                      立即支付
                    </UButton>
                  </template>

                  <template v-else-if="order.status === 'SHIPPED'">
                    <UButton
                      size="sm"
                      @click="confirmOrder(order.id)"
                    >
                      确认收货
                    </UButton>
                  </template>

                  <template v-else-if="order.status === 'DELIVERED'">
                    <UButton
                      variant="outline"
                      size="sm"
                      @click="reviewOrder(order.id)"
                    >
                      评价商品
                    </UButton>
                    <UButton
                      variant="outline"
                      size="sm"
                      color="red"
                      @click="requestRefund(order.id)"
                    >
                      申请退款
                    </UButton>
                  </template>

                  <UButton
                    variant="outline"
                    size="sm"
                    @click="buyAgain(order)"
                  >
                    再次购买
                  </UButton>
                </div>
              </UCard>
            </div>

            <!-- 分页 -->
            <div v-if="ordersStore.pagination.totalPages > 1" class="flex justify-center mt-8">
              <UPagination
                v-model="ordersStore.pagination.page"
                :page-count="ordersStore.pagination.pageSize"
                :total="ordersStore.pagination.total"
                @update:model-value="handlePageChange"
              />
            </div>
          </div>
        </template>
      </UTabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Order } from '~/types'

// 页面元信息
definePageMeta({
  middleware: 'auth'
})

// 页面SEO
useHead({
  title: '我的订单 - 社交购物网站'
})

// 状态管理
const ordersStore = useOrdersStore()
const cartStore = useCartStore()
const toast = useToast()

// 响应式数据
const activeTab = ref(0)

// 状态筛选标签
const statusTabs = [
  { key: 'all', label: '全部订单' },
  { key: 'PENDING', label: '待付款' },
  { key: 'PAID', label: '待发货' },
  { key: 'SHIPPED', label: '待收货' },
  { key: 'DELIVERED', label: '已完成' },
  { key: 'CANCELLED', label: '已取消' }
]

// 计算属性
const filteredOrders = computed(() => {
  const currentTab = statusTabs[activeTab.value]
  if (currentTab.key === 'all') {
    return ordersStore.orders
  }
  return ordersStore.getOrdersByStatus(currentTab.key)
})

// 获取订单状态颜色
const getOrderStatusColor = (status: string) => {
  const colors = {
    'PENDING': 'yellow',
    'PAID': 'blue',
    'SHIPPED': 'purple',
    'DELIVERED': 'green',
    'CANCELLED': 'red',
    'REFUNDED': 'gray'
  }
  return colors[status as keyof typeof colors] || 'gray'
}

// 获取订单状态文本
const getOrderStatusText = (status: string) => {
  const texts = {
    'PENDING': '待付款',
    'PAID': '待发货',
    'SHIPPED': '待收货',
    'DELIVERED': '已完成',
    'CANCELLED': '已取消',
    'REFUNDED': '已退款'
  }
  return texts[status as keyof typeof texts] || status
}

// 获取支付状态颜色
const getPaymentStatusColor = (status: string) => {
  const colors = {
    'PENDING': 'yellow',
    'PAID': 'green',
    'FAILED': 'red',
    'REFUNDED': 'gray'
  }
  return colors[status as keyof typeof colors] || 'gray'
}

// 获取支付状态文本
const getPaymentStatusText = (status: string) => {
  const texts = {
    'PENDING': '待支付',
    'PAID': '已支付',
    'FAILED': '支付失败',
    'REFUNDED': '已退款'
  }
  return texts[status as keyof typeof texts] || status
}

// 格式化日期
const formatDate = (date: string | Date) => {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 处理标签切换
const handleTabChange = (index: number) => {
  activeTab.value = index
  ordersStore.pagination.page = 1
  fetchOrders()
}

// 处理分页
const handlePageChange = (page: number) => {
  ordersStore.pagination.page = page
  fetchOrders()
}

// 获取订单列表
const fetchOrders = async () => {
  const currentTab = statusTabs[activeTab.value]
  const params: Record<string, any> = {
    page: ordersStore.pagination.page,
    pageSize: ordersStore.pagination.pageSize
  }
  
  if (currentTab.key !== 'all') {
    params.status = currentTab.key
  }
  
  await ordersStore.fetchOrders(params)
}

// 查看订单详情
const viewOrderDetail = (orderId: number) => {
  navigateTo(`/orders/${orderId}`)
}

// 取消订单
const cancelOrder = async (orderId: number) => {
  if (confirm('确定要取消这个订单吗？')) {
    try {
      await ordersStore.cancelOrder(orderId)
      await fetchOrders()
    } catch (error) {
      console.error('取消订单失败:', error)
    }
  }
}

// 支付订单
const payOrder = (orderId: number) => {
  navigateTo(`/payment/${orderId}`)
}

// 确认收货
const confirmOrder = async (orderId: number) => {
  if (confirm('确认已收到商品吗？')) {
    try {
      await ordersStore.confirmOrder(orderId)
      await fetchOrders()
    } catch (error) {
      console.error('确认收货失败:', error)
    }
  }
}

// 评价订单
const reviewOrder = (orderId: number) => {
  navigateTo(`/orders/${orderId}/review`)
}

// 申请退款
const requestRefund = async (orderId: number) => {
  const reason = prompt('请输入退款原因：')
  if (reason) {
    try {
      await ordersStore.requestRefund(orderId, reason)
    } catch (error) {
      console.error('申请退款失败:', error)
    }
  }
}

// 再次购买
const buyAgain = async (order: Order) => {
  try {
    // 将订单商品添加到购物车
    for (const item of order.items) {
      await cartStore.addItem(item.productId, item.quantity)
    }
    
    toast.add({
      title: '商品已添加到购物车',
      color: 'green'
    })
    
    navigateTo('/cart')
  } catch (error) {
    console.error('再次购买失败:', error)
  }
}

// 页面加载时获取订单列表
onMounted(() => {
  fetchOrders()
})
</script>
