version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: social-shop-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: social_shop
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    networks:
      - social-shop-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: social-shop-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis123
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - social-shop-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # pgAdmin (PostgreSQL管理工具)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: social-shop-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - social-shop-network
    depends_on:
      - postgres

  # Redis Commander (Redis管理工具)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: social-shop-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379:0:redis123
      HTTP_USER: admin
      HTTP_PASSWORD: admin123
    ports:
      - "8081:8081"
    networks:
      - social-shop-network
    depends_on:
      - redis

  # MailHog (邮件测试工具)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: social-shop-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP端口
      - "8025:8025"  # Web界面端口
    networks:
      - social-shop-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  social-shop-network:
    driver: bridge
