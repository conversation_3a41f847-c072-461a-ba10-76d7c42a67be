/**
 * 获取用户搜索历史
 * GET /api/search/history
 */

export default defineApiHandler(async (event) => {
  // 获取当前用户ID
  const userId = event.context.user?.id
  if (!userId) {
    throw new AuthenticationError('请先登录')
  }

  const query = getQuery(event)
  const limit = Math.min(parseInt(query.limit as string) || 10, 50)

  try {
    // 在实际项目中，这里应该从数据库查询用户的搜索历史
    // 这里使用模拟数据
    const mockHistory = [
      {
        id: 1,
        keyword: '手机',
        searchTime: new Date('2024-01-20T10:30:00'),
        resultCount: 156
      },
      {
        id: 2,
        keyword: '电脑',
        searchTime: new Date('2024-01-19T15:20:00'),
        resultCount: 89
      },
      {
        id: 3,
        keyword: '运动鞋',
        searchTime: new Date('2024-01-18T09:15:00'),
        resultCount: 234
      },
      {
        id: 4,
        keyword: '化妆品',
        searchTime: new Date('2024-01-17T14:45:00'),
        resultCount: 67
      },
      {
        id: 5,
        keyword: '家居用品',
        searchTime: new Date('2024-01-16T11:30:00'),
        resultCount: 123
      }
    ]

    // 按时间倒序排序并限制数量
    const history = mockHistory
      .sort((a, b) => b.searchTime.getTime() - a.searchTime.getTime())
      .slice(0, limit)

    return createSuccessResponse({
      items: history,
      count: history.length
    }, '获取搜索历史成功')

  } catch (error) {
    console.error('获取搜索历史失败:', error)
    throw new InternalServerError('获取搜索历史失败')
  }
})
