/**
 * 获取日志统计信息
 * GET /api/admin/logs/stats
 */

export default defineApiHandler(async (event) => {
  // 检查管理员权限
  const user = event.context.user
  if (!user || user.role !== 'admin') {
    throw new AuthorizationError('需要管理员权限')
  }

  const query = getQuery(event)
  const days = parseInt(query.days as string) || 7 // 默认最近7天

  try {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    // 搜索日志统计
    const searchStats = await prisma.searchLog.aggregate({
      where: {
        createdAt: {
          gte: startDate
        }
      },
      _count: {
        id: true
      },
      _avg: {
        resultCount: true
      },
      _sum: {
        resultCount: true
      }
    })

    // 用户操作日志统计
    const userLogStats = await prisma.userLog.aggregate({
      where: {
        createdAt: {
          gte: startDate
        }
      },
      _count: {
        id: true
      }
    })

    // 每日搜索趋势
    const dailySearchTrend = await prisma.$queryRaw`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count,
        AVG(result_count) as avg_results
      FROM search_logs 
      WHERE created_at >= ${startDate}
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `

    // 每日用户活动趋势
    const dailyUserActivityTrend = await prisma.$queryRaw`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count,
        COUNT(DISTINCT user_id) as unique_users
      FROM user_logs 
      WHERE created_at >= ${startDate}
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `

    // 热门搜索关键词
    const popularKeywords = await prisma.searchLog.groupBy({
      by: ['keyword'],
      where: {
        createdAt: {
          gte: startDate
        }
      },
      _count: {
        keyword: true
      },
      _avg: {
        resultCount: true
      },
      orderBy: {
        _count: {
          keyword: 'desc'
        }
      },
      take: 20
    })

    // 用户操作类型统计
    const actionTypeStats = await prisma.userLog.groupBy({
      by: ['action'],
      where: {
        createdAt: {
          gte: startDate
        }
      },
      _count: {
        action: true
      },
      orderBy: {
        _count: {
          action: 'desc'
        }
      }
    })

    // 活跃用户统计
    const activeUsers = await prisma.userLog.groupBy({
      by: ['userId'],
      where: {
        createdAt: {
          gte: startDate
        },
        userId: {
          not: null
        }
      },
      _count: {
        userId: true
      },
      orderBy: {
        _count: {
          userId: 'desc'
        }
      },
      take: 10
    })

    // 获取活跃用户的详细信息
    const activeUserIds = activeUsers.map(u => u.userId).filter(Boolean)
    const activeUserDetails = await prisma.user.findMany({
      where: {
        id: {
          in: activeUserIds as number[]
        }
      },
      select: {
        id: true,
        username: true,
        nickname: true,
        email: true
      }
    })

    // 合并活跃用户信息
    const topActiveUsers = activeUsers.map(userStat => {
      const userDetail = activeUserDetails.find(u => u.id === userStat.userId)
      return {
        userId: userStat.userId,
        actionCount: userStat._count.userId,
        user: userDetail
      }
    })

    // IP地址统计
    const ipStats = await prisma.userLog.groupBy({
      by: ['ipAddress'],
      where: {
        createdAt: {
          gte: startDate
        },
        ipAddress: {
          not: null
        }
      },
      _count: {
        ipAddress: true
      },
      orderBy: {
        _count: {
          ipAddress: 'desc'
        }
      },
      take: 10
    })

    const result = {
      period: {
        days,
        startDate,
        endDate: new Date()
      },
      searchStats: {
        totalSearches: searchStats._count.id,
        averageResults: searchStats._avg.resultCount || 0,
        totalResults: searchStats._sum.resultCount || 0
      },
      userActivityStats: {
        totalActions: userLogStats._count.id,
        uniqueActiveUsers: activeUsers.length
      },
      trends: {
        dailySearches: dailySearchTrend,
        dailyUserActivity: dailyUserActivityTrend
      },
      popularKeywords: popularKeywords.map(item => ({
        keyword: item.keyword,
        searchCount: item._count.keyword,
        averageResults: item._avg.resultCount || 0
      })),
      actionTypeStats: actionTypeStats.map(item => ({
        action: item.action,
        count: item._count.action
      })),
      topActiveUsers,
      topIPs: ipStats.map(item => ({
        ipAddress: item.ipAddress,
        actionCount: item._count.ipAddress
      }))
    }

    return createSuccessResponse(result, '获取日志统计成功')

  } catch (error) {
    console.error('获取日志统计失败:', error)
    throw new InternalServerError('获取日志统计失败')
  }
})
