import type { ApiResponse } from '~/types'

/**
 * API请求工具
 * 自动处理认证头和错误处理
 */

// 请求配置接口
interface RequestOptions {
  params?: Record<string, any>
  requireAuth?: boolean
  headers?: HeadersInit
  body?: any
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
}

/**
 * 创建带认证的请求实例
 */
export function createAuthenticatedRequest() {
  const authStore = useAuthStore()

  return {
    get: <T = any>(url: string, options: RequestOptions = {}) => request<T>(url, { ...options, method: 'GET' }),

    post: <T = any>(url: string, data?: any, options: RequestOptions = {}) =>
      request<T>(url, { ...options, method: 'POST', body: data }),

    put: <T = any>(url: string, data?: any, options: RequestOptions = {}) =>
      request<T>(url, { ...options, method: 'PUT', body: data }),

    delete: <T = any>(url: string, options: RequestOptions = {}) => request<T>(url, { ...options, method: 'DELETE' }),

    patch: <T = any>(url: string, data?: any, options: RequestOptions = {}) =>
      request<T>(url, { ...options, method: 'PATCH', body: data })
  }

  async function request<T>(url: string, options: RequestOptions = {}): Promise<T> {
    const { params, requireAuth = true, headers: optionHeaders, body, method, ...otherOptions } = options

    // 构建URL参数
    let requestUrl = url
    if (params) {
      const searchParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      const queryString = searchParams.toString()
      if (queryString) {
        requestUrl += (url.includes('?') ? '&' : '?') + queryString
      }
    }

    // 设置请求头
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...optionHeaders
    }

    // 添加认证头
    if (requireAuth && authStore.token) {
      ;(headers as Record<string, string>).Authorization = `Bearer ${authStore.token}`
    }

    try {
      const response = await $fetch<ApiResponse<T>>(requestUrl, {
        method,
        body: body ? JSON.stringify(body) : undefined,
        headers
      })

      // 检查响应状态
      if (!response.success) {
        throw new Error(response.message || '请求失败')
      }

      return response.data as T
    } catch (error: any) {
      // 处理认证错误
      if (error.statusCode === 401) {
        // Token过期或无效，清除认证状态
        authStore.clearAuth()

        // 如果不是在登录页面，跳转到登录页
        const route = useRoute()
        if (route.path !== '/login') {
          await navigateTo(`/login?redirect=${encodeURIComponent(route.fullPath)}`)
        }
      }

      // 抛出错误供调用方处理
      throw error
    }
  }
}

/**
 * 全局API请求实例
 */
export const api = createAuthenticatedRequest()

/**
 * 上传文件
 */
export async function uploadFile(file: File, onProgress?: (progress: number) => void): Promise<string> {
  const authStore = useAuthStore()

  return new Promise((resolve, reject) => {
    const formData = new FormData()
    formData.append('file', file)

    const xhr = new XMLHttpRequest()

    // 监听上传进度
    if (onProgress) {
      xhr.upload.addEventListener('progress', event => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100)
          onProgress(progress)
        }
      })
    }

    // 监听响应
    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        try {
          const response = JSON.parse(xhr.responseText)
          if (response.success) {
            resolve(response.data.url)
          } else {
            reject(new Error(response.message || '上传失败'))
          }
        } catch (error) {
          reject(new Error('响应解析失败'))
        }
      } else {
        reject(new Error(`上传失败: ${xhr.status}`))
      }
    })

    // 监听错误
    xhr.addEventListener('error', () => {
      reject(new Error('网络错误'))
    })

    // 设置请求头
    if (authStore.token) {
      xhr.setRequestHeader('Authorization', `Bearer ${authStore.token}`)
    }

    // 发送请求
    xhr.open('POST', '/api/upload')
    xhr.send(formData)
  })
}

/**
 * 下载文件
 */
export async function downloadFile(url: string, filename?: string): Promise<void> {
  try {
    const response = await fetch(url)
    const blob = await response.blob()

    // 创建下载链接
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理URL对象
    window.URL.revokeObjectURL(downloadUrl)
  } catch (error) {
    console.error('下载失败:', error)
    throw error
  }
}

/**
 * 批量请求
 */
export async function batchRequest<T>(requests: Array<() => Promise<T>>): Promise<Array<T | Error>> {
  return Promise.allSettled(requests.map(request => request())).then(results =>
    results.map(result => (result.status === 'fulfilled' ? result.value : result.reason))
  )
}

/**
 * 重试请求
 */
export async function retryRequest<T>(
  requestFn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await requestFn()
    } catch (error) {
      lastError = error as Error

      // 如果是最后一次重试，直接抛出错误
      if (i === maxRetries) {
        throw lastError
      }

      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
    }
  }

  throw lastError!
}
