/**
 * API访问日志中间件
 * 自动记录重要的API访问日志
 */

import { logUserAction } from '~/server/utils/logger'

// 需要记录日志的API路径和对应的操作类型
const API_LOG_MAPPING: Record<string, string> = {
  // 订单相关
  'POST:/api/orders': 'ORDER_CREATE',
  'POST:/api/orders/[id]/pay': 'ORDER_PAY',
  'POST:/api/orders/[id]/cancel': 'ORDER_CANCEL',
  
  // 收藏相关
  'POST:/api/favorites': 'FAVORITE_ADD',
  'DELETE:/api/favorites/[id]': 'FAVORITE_REMOVE',
  
  // 评价相关
  'POST:/api/reviews': 'REVIEW_CREATE',
  
  // 优惠券相关
  'POST:/api/coupons/[id]/claim': 'COUPON_CLAIM',
  
  // 社交相关
  'POST:/api/posts': 'POST_CREATE',
  'POST:/api/posts/[id]/like': 'POST_LIKE',
  'POST:/api/comments': 'COMMENT_CREATE',
  'POST:/api/users/[id]/follow': 'FOLLOW_USER',
  'DELETE:/api/users/[id]/follow': 'UNFOLLOW_USER',
  
  // 个人信息相关
  'PUT:/api/profile': 'PROFILE_UPDATE',
  'POST:/api/auth/change-password': 'PASSWORD_CHANGE'
}

// 不需要记录日志的路径模式
const SKIP_LOG_PATTERNS = [
  '/api/auth/me',
  '/api/health',
  '/api/admin/logs',
  '/api/cron'
]

export default defineEventHandler(async (event) => {
  // 只处理API请求
  if (!event.node.req.url?.startsWith('/api/')) {
    return
  }

  // 检查是否需要跳过日志记录
  const url = event.node.req.url
  const shouldSkip = SKIP_LOG_PATTERNS.some(pattern => url.includes(pattern))
  if (shouldSkip) {
    return
  }

  // 获取请求方法和路径
  const method = event.node.req.method || 'GET'
  const path = url.split('?')[0] // 移除查询参数
  
  // 构建API标识符
  const apiKey = `${method}:${path}`
  
  // 检查是否需要记录此API的日志
  let actionType = API_LOG_MAPPING[apiKey]
  
  // 如果没有精确匹配，尝试模式匹配
  if (!actionType) {
    for (const [pattern, action] of Object.entries(API_LOG_MAPPING)) {
      const [patternMethod, patternPath] = pattern.split(':')
      if (method === patternMethod && matchPath(path, patternPath)) {
        actionType = action
        break
      }
    }
  }

  // 如果找到了对应的操作类型，记录日志
  if (actionType) {
    // 在请求处理完成后记录日志
    event.node.res.on('finish', async () => {
      try {
        const userId = event.context.user?.id
        const statusCode = event.node.res.statusCode
        
        // 只记录成功的操作（2xx状态码）
        if (statusCode >= 200 && statusCode < 300) {
          // 提取资源信息
          const resourceInfo = extractResourceInfo(path, actionType)
          
          await logUserAction(actionType, event, {
            userId,
            resource: resourceInfo.resource,
            resourceId: resourceInfo.resourceId,
            details: {
              method,
              path,
              statusCode,
              timestamp: new Date().toISOString()
            }
          })
        }
      } catch (error) {
        // 日志记录失败不应该影响正常的API响应
        console.error('API日志记录失败:', error)
      }
    })
  }
})

/**
 * 检查路径是否匹配模式
 * 支持 [id] 这样的动态参数
 */
function matchPath(actualPath: string, patternPath: string): boolean {
  const actualParts = actualPath.split('/').filter(Boolean)
  const patternParts = patternPath.split('/').filter(Boolean)
  
  if (actualParts.length !== patternParts.length) {
    return false
  }
  
  for (let i = 0; i < patternParts.length; i++) {
    const patternPart = patternParts[i]
    const actualPart = actualParts[i]
    
    // 如果是动态参数（用[]包围），跳过检查
    if (patternPart.startsWith('[') && patternPart.endsWith(']')) {
      continue
    }
    
    // 精确匹配
    if (patternPart !== actualPart) {
      return false
    }
  }
  
  return true
}

/**
 * 从路径中提取资源信息
 */
function extractResourceInfo(path: string, actionType: string): {
  resource?: string
  resourceId?: number
} {
  const parts = path.split('/').filter(Boolean)
  
  // 根据操作类型确定资源类型
  let resource: string | undefined
  let resourceId: number | undefined
  
  switch (actionType) {
    case 'ORDER_CREATE':
    case 'ORDER_PAY':
    case 'ORDER_CANCEL':
      resource = 'order'
      // 对于订单操作，尝试从路径中提取订单ID
      const orderIdIndex = parts.indexOf('orders') + 1
      if (orderIdIndex < parts.length) {
        const id = parseInt(parts[orderIdIndex])
        if (!isNaN(id)) {
          resourceId = id
        }
      }
      break
      
    case 'FAVORITE_ADD':
    case 'FAVORITE_REMOVE':
      resource = 'product'
      // 收藏操作通常针对商品
      break
      
    case 'REVIEW_CREATE':
      resource = 'product'
      break
      
    case 'COUPON_CLAIM':
      resource = 'coupon'
      const couponIdIndex = parts.indexOf('coupons') + 1
      if (couponIdIndex < parts.length) {
        const id = parseInt(parts[couponIdIndex])
        if (!isNaN(id)) {
          resourceId = id
        }
      }
      break
      
    case 'POST_CREATE':
    case 'POST_LIKE':
      resource = 'post'
      const postIdIndex = parts.indexOf('posts') + 1
      if (postIdIndex < parts.length && actionType === 'POST_LIKE') {
        const id = parseInt(parts[postIdIndex])
        if (!isNaN(id)) {
          resourceId = id
        }
      }
      break
      
    case 'COMMENT_CREATE':
      resource = 'comment'
      break
      
    case 'FOLLOW_USER':
    case 'UNFOLLOW_USER':
      resource = 'user'
      const userIdIndex = parts.indexOf('users') + 1
      if (userIdIndex < parts.length) {
        const id = parseInt(parts[userIdIndex])
        if (!isNaN(id)) {
          resourceId = id
        }
      }
      break
  }
  
  return { resource, resourceId }
}
