import { z } from 'zod'

/**
 * 发布动态
 * POST /api/posts
 */

// 请求数据验证schema
const createPostSchema = z.object({
  content: z.string().min(1, '动态内容不能为空').max(2000, '动态内容最多2000个字符'),
  images: z.array(z.string().url('图片链接格式不正确')).max(9, '最多上传9张图片').optional(),
  type: z.enum(['TEXT', 'IMAGE', 'PRODUCT_SHARE']).default('TEXT'),
  productId: z.number().int().positive().optional()
})

export default defineApiHandler(async (event) => {
  // 只允许POST请求
  assertMethod(event, 'POST')

  // 获取当前用户ID
  const userId = event.context.user?.id
  if (!userId) {
    throw new AuthenticationError('请先登录')
  }

  // 获取请求体数据
  const body = await readBody(event)

  // 验证请求数据
  const { content, images, type, productId } = createPostSchema.parse(body)

  // 验证动态类型和相关数据
  if (type === 'IMAGE' && (!images || images.length === 0)) {
    throw new ValidationError('图片动态必须包含至少一张图片')
  }

  if (type === 'PRODUCT_SHARE') {
    if (!productId) {
      throw new ValidationError('商品分享动态必须指定商品ID')
    }

    // 验证商品是否存在
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { id: true, status: true }
    })

    if (!product || product.status !== 'ACTIVE') {
      throw new NotFoundError('商品不存在或已下架')
    }
  }

  // 创建动态
  const post = await prisma.post.create({
    data: {
      userId,
      content,
      images: images || [],
      type,
      productId,
      status: 'PUBLISHED'
    },
    include: {
      user: {
        select: {
          id: true,
          username: true,
          nickname: true,
          avatar: true
        }
      },
      product: productId ? {
        select: {
          id: true,
          name: true,
          price: true,
          images: true,
          status: true
        }
      } : false
    }
  })

  // 格式化返回数据
  const formattedPost = {
    ...post,
    isLiked: false,
    product: post.product ? {
      ...post.product,
      price: post.product.price?.toNumber()
    } : null
  }

  return createSuccessResponse(formattedPost, '动态发布成功')
})
