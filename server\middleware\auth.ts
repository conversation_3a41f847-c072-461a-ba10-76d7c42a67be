/**
 * 服务端认证中间件
 * 验证JWT token并设置用户上下文
 */

export default defineEventHandler(async (event) => {
  // 只处理API请求
  if (!event.node.req.url?.startsWith('/api/')) {
    return
  }
  
  // 不需要认证的API路由
  const publicApiRoutes = [
    '/api/health',
    '/api/auth/login',
    '/api/auth/register',
    '/api/products',
    '/api/categories'
  ]
  
  // 检查是否为公开API
  const isPublicApi = publicApiRoutes.some(route => 
    event.node.req.url?.startsWith(route)
  )
  
  if (isPublicApi) {
    return
  }
  
  try {
    // 提取token
    const token = extractTokenFromHeader(event)
    
    if (!token) {
      throw createError({
        statusCode: 401,
        statusMessage: '未提供认证token'
      })
    }
    
    // 验证token
    const payload = verifyToken(token)
    
    if (!payload) {
      throw createError({
        statusCode: 401,
        statusMessage: 'token无效或已过期'
      })
    }
    
    // 将用户信息添加到事件上下文
    event.context.user = {
      id: payload.userId,
      username: payload.username,
      role: payload.role
    }
    
  } catch (error) {
    // 如果是已知的HTTP错误，直接抛出
    if (error.statusCode) {
      throw error
    }
    
    // 其他错误统一处理为认证失败
    throw createError({
      statusCode: 401,
      statusMessage: '认证失败'
    })
  }
})
