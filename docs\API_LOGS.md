# 📊 日志管理API文档

本文档详细介绍日志管理相关的API接口，包括搜索日志、用户操作日志的查询、统计和管理功能。

## 🔐 权限要求

所有日志管理API都需要**管理员权限**，请确保在请求头中包含有效的管理员JWT token：

```http
Authorization: Bearer <admin-jwt-token>
```

## 📋 API概览

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 搜索日志列表 | GET | `/api/admin/logs/search` | 获取搜索日志列表 |
| 用户操作日志列表 | GET | `/api/admin/logs/users` | 获取用户操作日志列表 |
| 日志统计信息 | GET | `/api/admin/logs/stats` | 获取日志统计和分析数据 |
| 清理过期日志 | POST | `/api/admin/logs/cleanup` | 手动清理过期日志 |
| 定时清理日志 | POST | `/api/cron/cleanup-logs` | 定时任务清理日志 |

## 🔍 搜索日志API

### 获取搜索日志列表

**接口地址**: `GET /api/admin/logs/search`

**功能说明**: 获取用户搜索行为日志，支持多种过滤和排序条件。

**查询参数**:

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| page | number | 否 | 1 | 页码 |
| pageSize | number | 否 | 20 | 每页数量 (1-100) |
| keyword | string | 否 | - | 搜索关键词过滤 |
| userId | number | 否 | - | 用户ID过滤 |
| startDate | string | 否 | - | 开始时间 (ISO 8601格式) |
| endDate | string | 否 | - | 结束时间 (ISO 8601格式) |
| minResults | number | 否 | - | 最小结果数过滤 |
| maxResults | number | 否 | - | 最大结果数过滤 |
| sortBy | string | 否 | createdAt | 排序字段 (createdAt, keyword, resultCount) |
| sortOrder | string | 否 | desc | 排序方向 (asc, desc) |

**响应示例**:

```json
{
  "success": true,
  "message": "获取搜索日志成功",
  "data": {
    "items": [
      {
        "id": 1,
        "keyword": "iPhone 15",
        "resultCount": 25,
        "userId": 123,
        "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "ipAddress": "*************",
        "filters": {
          "categoryId": 1,
          "minPrice": 5000,
          "maxPrice": 10000
        },
        "createdAt": "2024-01-15T10:30:00.000Z",
        "user": {
          "id": 123,
          "username": "john_doe",
          "nickname": "John",
          "email": "<EMAIL>"
        }
      }
    ],
    "total": 1500,
    "page": 1,
    "pageSize": 20,
    "totalPages": 75,
    "stats": {
      "totalSearches": 1500,
      "averageResults": 18.5,
      "totalResults": 27750
    },
    "popularKeywords": [
      {
        "keyword": "iPhone",
        "count": 150
      },
      {
        "keyword": "Samsung",
        "count": 120
      }
    ]
  }
}
```

## 👤 用户操作日志API

### 获取用户操作日志列表

**接口地址**: `GET /api/admin/logs/users`

**功能说明**: 获取用户操作行为日志，用于安全审计和行为分析。

**查询参数**:

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| page | number | 否 | 1 | 页码 |
| pageSize | number | 否 | 20 | 每页数量 (1-100) |
| userId | number | 否 | - | 用户ID过滤 |
| action | string | 否 | - | 操作类型过滤 |
| resource | string | 否 | - | 资源类型过滤 |
| resourceId | number | 否 | - | 资源ID过滤 |
| startDate | string | 否 | - | 开始时间 (ISO 8601格式) |
| endDate | string | 否 | - | 结束时间 (ISO 8601格式) |
| ipAddress | string | 否 | - | IP地址过滤 |
| sortBy | string | 否 | createdAt | 排序字段 (createdAt, action, userId) |
| sortOrder | string | 否 | desc | 排序方向 (asc, desc) |

**操作类型枚举**:

- `LOGIN` - 登录
- `LOGOUT` - 登出
- `REGISTER` - 注册
- `PRODUCT_VIEW` - 查看商品
- `ORDER_CREATE` - 创建订单
- `CART_ADD` - 添加到购物车
- `FAVORITE_ADD` - 添加收藏
- 更多操作类型请参考主API文档

**响应示例**:

```json
{
  "success": true,
  "message": "获取用户日志成功",
  "data": {
    "items": [
      {
        "id": 1001,
        "userId": 123,
        "action": "LOGIN",
        "resource": null,
        "resourceId": null,
        "details": {
          "loginMethod": "email",
          "success": true,
          "timestamp": "2024-01-15T09:00:00.000Z"
        },
        "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)",
        "ipAddress": "*************",
        "createdAt": "2024-01-15T09:00:00.000Z",
        "user": {
          "id": 123,
          "username": "john_doe",
          "nickname": "John",
          "email": "<EMAIL>",
          "role": "USER"
        }
      }
    ],
    "total": 5000,
    "page": 1,
    "pageSize": 20,
    "totalPages": 250,
    "stats": {
      "actionStats": [
        {
          "action": "LOGIN",
          "count": 500
        },
        {
          "action": "PRODUCT_VIEW",
          "count": 1200
        }
      ],
      "activeUsersCount": 150,
      "topActiveUsers": [
        {
          "userId": 123,
          "actionCount": 45
        }
      ]
    }
  }
}
```

## 📈 日志统计API

### 获取日志统计信息

**接口地址**: `GET /api/admin/logs/stats`

**功能说明**: 获取日志的统计分析数据，包括趋势图表、热门关键词、活跃用户等。

**查询参数**:

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| days | number | 否 | 7 | 统计天数 (1-365) |

**响应示例**:

```json
{
  "success": true,
  "message": "获取日志统计成功",
  "data": {
    "period": {
      "days": 7,
      "startDate": "2024-01-08T00:00:00.000Z",
      "endDate": "2024-01-15T00:00:00.000Z"
    },
    "searchStats": {
      "totalSearches": 2500,
      "averageResults": 16.8,
      "totalResults": 42000
    },
    "userActivityStats": {
      "totalActions": 12000,
      "uniqueActiveUsers": 350
    },
    "trends": {
      "dailySearches": [
        {
          "date": "2024-01-08",
          "count": 300,
          "avg_results": 15.5
        }
      ],
      "dailyUserActivity": [
        {
          "date": "2024-01-08",
          "count": 1500,
          "unique_users": 80
        }
      ]
    },
    "popularKeywords": [
      {
        "keyword": "iPhone 15",
        "searchCount": 200,
        "averageResults": 25.5
      }
    ],
    "actionTypeStats": [
      {
        "action": "LOGIN",
        "count": 800
      }
    ],
    "topActiveUsers": [
      {
        "userId": 123,
        "actionCount": 65,
        "user": {
          "id": 123,
          "username": "john_doe",
          "nickname": "John",
          "email": "<EMAIL>"
        }
      }
    ],
    "topIPs": [
      {
        "ipAddress": "*************",
        "actionCount": 150
      }
    ]
  }
}
```

## 🧹 日志清理API

### 手动清理过期日志

**接口地址**: `POST /api/admin/logs/cleanup`

**功能说明**: 管理员手动清理过期的日志数据。

**请求体**:

```json
{
  "daysToKeep": 90,
  "logTypes": ["search", "user"]
}
```

**参数说明**:

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| daysToKeep | number | 否 | 90 | 保留天数 (1-365) |
| logTypes | array | 否 | ["all"] | 日志类型: search, user, all |

### 定时清理日志

**接口地址**: `POST /api/cron/cleanup-logs`

**功能说明**: 定时任务自动清理过期日志，需要特殊的认证方式。

**认证方式**:

```http
Authorization: Bearer <CRON_SECRET>
```

其中 `CRON_SECRET` 是环境变量中配置的定时任务密钥。

## 🔧 错误处理

### 常见错误码

| 状态码 | 错误类型 | 说明 |
|--------|----------|------|
| 401 | AuthenticationError | 未登录或token无效 |
| 403 | AuthorizationError | 权限不足，需要管理员权限 |
| 400 | ValidationError | 请求参数验证失败 |
| 500 | InternalServerError | 服务器内部错误 |

### 错误响应示例

```json
{
  "success": false,
  "message": "权限不足",
  "errors": ["需要管理员权限"]
}
```

## 💡 使用建议

1. **分页查询**: 日志数据量较大，建议使用分页查询避免性能问题
2. **时间范围**: 查询大量数据时建议指定时间范围
3. **定期清理**: 设置定时任务定期清理过期日志，避免数据库膨胀
4. **监控告警**: 可基于日志统计数据设置监控告警
5. **数据导出**: 重要日志数据建议定期导出备份
6. **隐私保护**: 日志中包含用户行为数据，请妥善保护用户隐私

## 🔗 相关文档

- [日志系统文档](./LOGGING.md) - 详细的日志系统说明
- [API文档](./API.md) - 完整的API接口文档
- [配置指南](./CONFIGURATION.md) - 系统配置说明
