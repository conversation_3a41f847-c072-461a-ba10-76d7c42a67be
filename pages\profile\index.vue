<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">个人中心</h1>
      <p class="text-gray-600 mt-2">管理您的个人信息和账户设置</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- 左侧个人信息卡片 -->
      <div class="lg:col-span-1">
        <UCard class="p-6">
          <div class="text-center">
            <!-- 头像 -->
            <div class="relative inline-block mb-4">
              <img
                :src="userProfile?.avatar || '/images/default-avatar.jpg'"
                :alt="userProfile?.nickname || userProfile?.username"
                class="w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg"
              />
              <UButton
                size="sm"
                variant="solid"
                class="absolute bottom-0 right-0 rounded-full p-2"
                @click="showAvatarUpload = true"
              >
                <Icon name="heroicons:camera" class="w-4 h-4" />
              </UButton>
            </div>

            <!-- 用户信息 -->
            <h2 class="text-xl font-semibold text-gray-900 mb-1">
              {{ userProfile?.nickname || userProfile?.username }}
            </h2>
            <p class="text-gray-500 text-sm mb-2">@{{ userProfile?.username }}</p>

            <!-- 角色标签 -->
            <UBadge :color="getRoleColor(userProfile?.role)" variant="subtle" class="mb-4">
              {{ getRoleText(userProfile?.role) }}
            </UBadge>

            <!-- 个人简介 -->
            <p v-if="userProfile?.bio" class="text-gray-600 text-sm mb-4">
              {{ userProfile.bio }}
            </p>

            <!-- 统计信息 -->
            <div class="grid grid-cols-3 gap-4 text-center border-t pt-4">
              <div>
                <div class="text-lg font-semibold text-gray-900">
                  {{ (userProfile as any)?.postsCount || 0 }}
                </div>
                <div class="text-xs text-gray-500">动态</div>
              </div>
              <div>
                <div class="text-lg font-semibold text-gray-900">
                  {{ (userProfile as any)?.followersCount || 0 }}
                </div>
                <div class="text-xs text-gray-500">粉丝</div>
              </div>
              <div>
                <div class="text-lg font-semibold text-gray-900">
                  {{ (userProfile as any)?.followingCount || 0 }}
                </div>
                <div class="text-xs text-gray-500">关注</div>
              </div>
            </div>
          </div>
        </UCard>

        <!-- 快捷操作 -->
        <UCard class="p-4 mt-6">
          <h3 class="font-semibold text-gray-900 mb-4">快捷操作</h3>
          <div class="space-y-2">
            <UButton variant="ghost" block justify="start" @click="navigateTo('/profile/edit')">
              <Icon name="heroicons:user" class="w-4 h-4 mr-2" />
              编辑个人信息
            </UButton>
            <UButton variant="ghost" block justify="start" @click="navigateTo('/profile/security')">
              <Icon name="heroicons:shield-check" class="w-4 h-4 mr-2" />
              账户安全
            </UButton>
            <UButton variant="ghost" block justify="start" @click="navigateTo('/orders')">
              <Icon name="heroicons:shopping-bag" class="w-4 h-4 mr-2" />
              我的订单
            </UButton>
            <UButton variant="ghost" block justify="start" @click="navigateTo('/favorites')">
              <Icon name="heroicons:heart" class="w-4 h-4 mr-2" />
              我的收藏
            </UButton>
          </div>
        </UCard>
      </div>

      <!-- 右侧内容区域 -->
      <div class="lg:col-span-2">
        <!-- 功能标签页 -->
        <UCard class="mb-6">
          <UTabs v-model="activeTab" :items="tabItems" class="w-full">
            <!-- 账户概览 -->
            <template #overview="{ item }">
              <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">账户概览</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <!-- 基本信息 -->
                  <div>
                    <h4 class="font-medium text-gray-900 mb-3">基本信息</h4>
                    <div class="space-y-2 text-sm">
                      <div class="flex justify-between">
                        <span class="text-gray-500">邮箱：</span>
                        <span class="text-gray-900">{{ userProfile?.email }}</span>
                      </div>
                      <div class="flex justify-between">
                        <span class="text-gray-500">手机：</span>
                        <span class="text-gray-900">
                          {{ userProfile?.phone || '未绑定' }}
                        </span>
                      </div>
                      <div class="flex justify-between">
                        <span class="text-gray-500">注册时间：</span>
                        <span class="text-gray-900">
                          {{ formatDate(userProfile?.createdAt) }}
                        </span>
                      </div>
                    </div>
                  </div>

                  <!-- 账户状态 -->
                  <div>
                    <h4 class="font-medium text-gray-900 mb-3">账户状态</h4>
                    <div class="space-y-2 text-sm">
                      <div class="flex justify-between items-center">
                        <span class="text-gray-500">账户状态：</span>
                        <UBadge :color="userProfile?.status === 'active' ? 'green' : 'red'" variant="subtle">
                          {{ userProfile?.status === 'active' ? '正常' : '异常' }}
                        </UBadge>
                      </div>
                      <div class="flex justify-between items-center">
                        <span class="text-gray-500">邮箱验证：</span>
                        <UBadge color="green" variant="subtle">已验证</UBadge>
                      </div>
                      <div class="flex justify-between items-center">
                        <span class="text-gray-500">手机验证：</span>
                        <UBadge :color="userProfile?.phone ? 'green' : 'yellow'" variant="subtle">
                          {{ userProfile?.phone ? '已验证' : '未验证' }}
                        </UBadge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>

            <!-- 我的优惠券 -->
            <template #coupons="{ item }">
              <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg font-semibold text-gray-900">我的优惠券</h3>
                  <div class="flex space-x-2">
                    <UButton
                      v-for="status in couponStatusOptions"
                      :key="status.value"
                      :variant="selectedCouponStatus === status.value ? 'solid' : 'outline'"
                      size="sm"
                      @click="((selectedCouponStatus = status.value), fetchUserCoupons())"
                    >
                      {{ status.label }}
                    </UButton>
                  </div>
                </div>

                <div v-if="isLoadingCoupons" class="text-center py-8">
                  <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                  <p class="mt-2 text-gray-600">加载中...</p>
                </div>

                <div v-else-if="userCoupons.length === 0" class="text-center py-8">
                  <Icon name="heroicons:ticket" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p class="text-gray-500">暂无优惠券</p>
                </div>

                <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div
                    v-for="coupon in userCoupons"
                    :key="coupon.id"
                    class="border rounded-lg p-4"
                    :class="{
                      'border-red-200 bg-red-50': coupon.status === 'UNUSED',
                      'border-gray-200 bg-gray-50': coupon.status === 'USED',
                      'border-gray-300 bg-gray-100': coupon.status === 'EXPIRED'
                    }"
                  >
                    <div class="flex items-center justify-between mb-2">
                      <span
                        class="px-2 py-1 text-xs rounded"
                        :class="
                          coupon.type === 'SHARED' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
                        "
                      >
                        {{ coupon.type === 'SHARED' ? '同享券' : '互斥券' }}
                      </span>
                      <span class="font-medium text-red-600">
                        {{ coupon.discountType === 'FIXED' ? `¥${coupon.discountValue}` : `${coupon.discountValue}%` }}
                      </span>
                    </div>
                    <h4 class="font-medium text-gray-900 mb-1">{{ coupon.name }}</h4>
                    <p class="text-sm text-gray-600 mb-2">满{{ coupon.minOrderAmount }}元可用</p>
                    <p class="text-xs text-gray-500">有效期至：{{ formatDate(coupon.expiresAt) }}</p>
                  </div>
                </div>
              </div>
            </template>

            <!-- 我的订单 -->
            <template #orders="{ item }">
              <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg font-semibold text-gray-900">我的订单</h3>
                  <div class="flex space-x-2">
                    <UButton
                      v-for="status in orderStatusOptions"
                      :key="status.value"
                      :variant="selectedOrderStatus === status.value ? 'solid' : 'outline'"
                      size="sm"
                      @click="((selectedOrderStatus = status.value), fetchUserOrders())"
                    >
                      {{ status.label }}
                    </UButton>
                  </div>
                </div>

                <div v-if="isLoadingOrders" class="text-center py-8">
                  <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                  <p class="mt-2 text-gray-600">加载中...</p>
                </div>

                <div v-else-if="userOrders.length === 0" class="text-center py-8">
                  <Icon name="heroicons:shopping-bag" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p class="text-gray-500">暂无订单</p>
                </div>

                <div v-else class="space-y-4">
                  <div
                    v-for="order in userOrders"
                    :key="order.id"
                    class="border rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div class="flex items-center justify-between mb-3">
                      <div>
                        <span class="text-sm text-gray-500">订单号：{{ order.orderNumber }}</span>
                        <UBadge :color="getOrderStatusColor(order.status)" variant="subtle" class="ml-2">
                          {{ getOrderStatusText(order.status) }}
                        </UBadge>
                      </div>
                      <span class="font-medium text-lg">¥{{ order.totalAmount }}</span>
                    </div>

                    <div class="flex items-center space-x-4 mb-3">
                      <img
                        :src="order.items[0]?.product?.images[0] || '/images/placeholder.jpg'"
                        :alt="order.items[0]?.product?.name"
                        class="w-16 h-16 object-cover rounded"
                      />
                      <div class="flex-1">
                        <h4 class="font-medium text-gray-900">{{ order.items[0]?.product?.name }}</h4>
                        <p class="text-sm text-gray-500">
                          {{
                            order.items.length > 1
                              ? `等${order.items.length}件商品`
                              : `数量：${order.items[0]?.quantity}`
                          }}
                        </p>
                      </div>
                    </div>

                    <div class="flex items-center justify-between text-sm text-gray-500">
                      <span>{{ formatDate(order.createdAt) }}</span>
                      <div class="space-x-2">
                        <UButton size="sm" variant="outline" @click="navigateTo(`/orders/${order.id}`)">
                          查看详情
                        </UButton>
                        <UButton
                          v-if="order.status === 'PENDING'"
                          size="sm"
                          @click="navigateTo(`/payment/${order.id}`)"
                        >
                          立即支付
                        </UButton>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>

            <!-- 我的收藏 -->
            <template #favorites="{ item }">
              <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg font-semibold text-gray-900">我的收藏</h3>
                  <UButton variant="outline" size="sm" @click="fetchUserFavorites">
                    <Icon name="heroicons:arrow-path" class="w-4 h-4 mr-1" />
                    刷新
                  </UButton>
                </div>

                <div v-if="isLoadingFavorites" class="text-center py-8">
                  <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                  <p class="mt-2 text-gray-600">加载中...</p>
                </div>

                <div v-else-if="userFavorites.length === 0" class="text-center py-8">
                  <Icon name="heroicons:heart" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p class="text-gray-500">暂无收藏商品</p>
                </div>

                <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div
                    v-for="favorite in userFavorites"
                    :key="favorite.id"
                    class="border rounded-lg overflow-hidden hover:shadow-md transition-shadow"
                  >
                    <div class="relative">
                      <img
                        :src="favorite.product.images[0] || '/images/placeholder.jpg'"
                        :alt="favorite.product.name"
                        class="w-full h-48 object-cover"
                      />
                      <UButton
                        variant="ghost"
                        size="sm"
                        class="absolute top-2 right-2 bg-white/80 hover:bg-white"
                        @click="toggleFavorite(favorite.product.id)"
                      >
                        <Icon name="heroicons:heart-solid" class="w-4 h-4 text-red-500" />
                      </UButton>
                    </div>

                    <div class="p-4">
                      <h4 class="font-medium text-gray-900 mb-2 line-clamp-2">{{ favorite.product.name }}</h4>
                      <div class="flex items-center justify-between">
                        <span class="text-lg font-bold text-red-600">¥{{ favorite.product.price }}</span>
                        <UButton size="sm" @click="navigateTo(`/products/${favorite.product.id}`)"> 查看详情 </UButton>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </UTabs>
        </UCard>

        <!-- 最近活动 -->
        <UCard class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">最近活动</h3>
            <UButton variant="ghost" size="sm" @click="navigateTo('/profile/activities')"> 查看全部 </UButton>
          </div>

          <div class="space-y-4">
            <div
              v-for="activity in recentActivities"
              :key="activity.id"
              class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg"
            >
              <div class="flex-shrink-0">
                <Icon :name="activity.icon" class="w-5 h-5 text-gray-400" />
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm text-gray-900">{{ activity.title }}</p>
                <p class="text-xs text-gray-500 mt-1">{{ activity.time }}</p>
              </div>
            </div>
          </div>
        </UCard>
      </div>
    </div>

    <!-- 头像上传模态框 -->
    <UModal v-model="showAvatarUpload">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">更换头像</h3>
        </template>

        <div class="p-4 text-center">
          <p class="text-gray-600 mb-4">选择新的头像图片</p>
          <UButton @click="handleAvatarUpload"> 选择图片 </UButton>
        </div>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import type { User } from '~/types'

// 页面元信息
definePageMeta({
  middleware: 'auth' as any
})

// 页面SEO
useHead({
  title: '个人中心 - 社交购物网站'
})

// 状态管理
const authStore = useAuthStore()
const toast = useToast()

// 响应式数据
const userProfile = ref<User | null>(null)
const showAvatarUpload = ref(false)
const isLoading = ref(true)

// 标签页相关
const activeTab = ref('overview')
const tabItems = [
  { key: 'overview', label: '账户概览', icon: 'heroicons:user' },
  { key: 'coupons', label: '我的优惠券', icon: 'heroicons:ticket' },
  { key: 'orders', label: '我的订单', icon: 'heroicons:shopping-bag' },
  { key: 'favorites', label: '我的收藏', icon: 'heroicons:heart' }
]

// 优惠券相关状态
const userCoupons = ref<any[]>([])
const isLoadingCoupons = ref(false)
const selectedCouponStatus = ref('UNUSED')
const couponStatusOptions = [
  { label: '未使用', value: 'UNUSED' },
  { label: '已使用', value: 'USED' },
  { label: '已过期', value: 'EXPIRED' }
]

// 订单相关状态
const userOrders = ref<any[]>([])
const isLoadingOrders = ref(false)
const selectedOrderStatus = ref('ALL')
const orderStatusOptions = [
  { label: '全部', value: 'ALL' },
  { label: '待支付', value: 'PENDING' },
  { label: '已支付', value: 'PAID' },
  { label: '已发货', value: 'SHIPPED' },
  { label: '已完成', value: 'COMPLETED' }
]

// 收藏相关状态
const userFavorites = ref<any[]>([])
const isLoadingFavorites = ref(false)

// 模拟最近活动数据
const recentActivities = ref([
  {
    id: 1,
    title: '更新了个人资料',
    time: '2小时前',
    icon: 'heroicons:user'
  },
  {
    id: 2,
    title: '发布了新动态',
    time: '1天前',
    icon: 'heroicons:document-text'
  },
  {
    id: 3,
    title: '关注了新用户',
    time: '2天前',
    icon: 'heroicons:user-plus'
  }
])

// 获取角色颜色
const getRoleColor = (role?: string) => {
  switch (role) {
    case 'ADMIN':
      return 'red'
    case 'MERCHANT':
      return 'blue'
    case 'USER':
      return 'green'
    default:
      return 'gray'
  }
}

// 获取角色文本
const getRoleText = (role?: string) => {
  switch (role) {
    case 'ADMIN':
      return '管理员'
    case 'MERCHANT':
      return '商家'
    case 'USER':
      return '用户'
    default:
      return '未知'
  }
}

// 格式化日期
const formatDate = (date?: Date | string) => {
  if (!date) return '未知'
  return new Date(date).toLocaleDateString('zh-CN')
}

// 获取用户优惠券
const fetchUserCoupons = async () => {
  try {
    isLoadingCoupons.value = true
    const response = await $fetch<any>('/api/users/coupons', {
      params: { status: selectedCouponStatus.value }
    })
    userCoupons.value = [...response.data.coupons.shared, ...response.data.coupons.exclusive]
  } catch (error) {
    console.error('获取优惠券失败:', error)
    toast.add({
      title: '获取优惠券失败',
      description: '请稍后重试',
      color: 'red'
    })
  } finally {
    isLoadingCoupons.value = false
  }
}

// 获取用户订单
const fetchUserOrders = async () => {
  try {
    isLoadingOrders.value = true
    const params: any = { page: 1, pageSize: 10 }
    if (selectedOrderStatus.value !== 'ALL') {
      params.status = selectedOrderStatus.value
    }

    const response = await $fetch<any>('/api/users/orders', { params })
    userOrders.value = response.data.items
  } catch (error) {
    console.error('获取订单失败:', error)
    toast.add({
      title: '获取订单失败',
      description: '请稍后重试',
      color: 'red'
    })
  } finally {
    isLoadingOrders.value = false
  }
}

// 获取用户收藏
const fetchUserFavorites = async () => {
  try {
    isLoadingFavorites.value = true
    const response = await $fetch<any>('/api/users/favorites')
    userFavorites.value = response.data.items
  } catch (error) {
    console.error('获取收藏失败:', error)
    toast.add({
      title: '获取收藏失败',
      description: '请稍后重试',
      color: 'red'
    })
  } finally {
    isLoadingFavorites.value = false
  }
}

// 切换收藏状态
const toggleFavorite = async (productId: number) => {
  try {
    await $fetch(`/api/products/${productId}/favorite`, {
      method: 'POST' as any
    })

    // 重新获取收藏列表
    await fetchUserFavorites()

    toast.add({
      title: '操作成功',
      color: 'green'
    })
  } catch (error) {
    console.error('操作失败:', error)
    toast.add({
      title: '操作失败',
      description: '请稍后重试',
      color: 'red'
    })
  }
}

// 获取订单状态颜色
const getOrderStatusColor = (status: string) => {
  switch (status) {
    case 'PENDING':
      return 'yellow'
    case 'PAID':
      return 'blue'
    case 'SHIPPED':
      return 'purple'
    case 'COMPLETED':
      return 'green'
    case 'CANCELLED':
      return 'red'
    default:
      return 'gray'
  }
}

// 获取订单状态文本
const getOrderStatusText = (status: string) => {
  switch (status) {
    case 'PENDING':
      return '待支付'
    case 'PAID':
      return '已支付'
    case 'SHIPPED':
      return '已发货'
    case 'COMPLETED':
      return '已完成'
    case 'CANCELLED':
      return '已取消'
    default:
      return '未知'
  }
}

// 处理头像上传
const handleAvatarUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  try {
    // 显示上传进度
    toast.add({
      title: '正在上传头像...',
      color: 'blue'
    })

    // 创建FormData
    const formData = new FormData()
    formData.append('files', file)

    // 调用头像上传API
    const response = await $fetch<any>('/api/upload/avatar', {
      method: 'POST',
      body: formData
    })

    // 更新用户头像
    if (response.data?.avatar && userProfile.value) {
      userProfile.value.avatar = response.data.avatar
      authStore.user!.avatar = response.data.avatar
    }

    // 显示成功提示
    toast.add({
      title: '头像上传成功',
      color: 'green'
    })
  } catch (error: any) {
    console.error('头像上传失败:', error)
    toast.add({
      title: '头像上传失败',
      description: error.data?.message || '请稍后重试',
      color: 'red'
    })
  }

  // 清空input
  target.value = ''
  showAvatarUpload.value = false
}

// 获取用户资料
const fetchUserProfile = async () => {
  try {
    isLoading.value = true
    await authStore.refreshUser()
    userProfile.value = authStore.user
  } catch (error) {
    console.error('获取用户资料失败:', error)
    toast.add({
      title: '获取用户资料失败',
      description: '请刷新页面重试',
      color: 'red'
    })
  } finally {
    isLoading.value = false
  }
}

// 监听标签页切换
watch(activeTab, newTab => {
  switch (newTab) {
    case 'coupons':
      fetchUserCoupons()
      break
    case 'orders':
      fetchUserOrders()
      break
    case 'favorites':
      fetchUserFavorites()
      break
  }
})

// 页面加载时获取数据
onMounted(() => {
  userProfile.value = authStore.user
  if (userProfile.value) {
    fetchUserProfile()
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.transition-shadow {
  transition: box-shadow 0.2s ease-in-out;
}

.hover\:shadow-md:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
</style>
