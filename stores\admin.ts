import type { User, Product, Order, PaginatedResponse } from '~/types'

/**
 * 管理员功能状态管理
 */
export const useAdminStore = defineStore('admin', () => {
  // 状态
  const users = ref<User[]>([])
  const products = ref<Product[]>([])
  const orders = ref<Order[]>([])
  const statistics = ref({
    totalUsers: 0,
    totalProducts: 0,
    totalOrders: 0,
    totalRevenue: 0,
    todayUsers: 0,
    todayOrders: 0,
    todayRevenue: 0,
    activeUsers: 0,
    pendingOrders: 0,
    reportedPosts: 0
  })
  const isLoading = ref(false)
  const usersPagination = ref({
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0
  })
  const productsPagination = ref({
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0
  })
  const ordersPagination = ref({
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0
  })

  // API实例
  const request = createAuthenticatedRequest()
  const authStore = useAuthStore()
  const toast = useToast()

  // 检查管理员权限
  const checkAdminPermission = () => {
    if (!authStore.isLoggedIn || !authStore.hasPermission('ADMIN')) {
      throw new Error('需要管理员权限')
    }
  }

  // 获取管理员统计数据
  const fetchStatistics = async () => {
    try {
      checkAdminPermission()
      
      const stats = await request.get('/api/admin/statistics')
      statistics.value = stats
    } catch (error: any) {
      console.error('获取统计数据失败:', error)
      toast.add({
        title: '获取统计数据失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
    }
  }

  // 获取用户列表
  const fetchUsers = async (params: Record<string, any> = {}) => {
    try {
      checkAdminPermission()
      isLoading.value = true
      
      const response = await request.get<PaginatedResponse<User>>('/api/admin/users', {
        params: {
          page: usersPagination.value.page,
          pageSize: usersPagination.value.pageSize,
          ...params
        }
      })
      
      users.value = response.items
      usersPagination.value = {
        page: response.page,
        pageSize: response.pageSize,
        total: response.total,
        totalPages: response.totalPages
      }
    } catch (error: any) {
      console.error('获取用户列表失败:', error)
      toast.add({
        title: '获取用户列表失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
    } finally {
      isLoading.value = false
    }
  }

  // 更新用户状态
  const updateUserStatus = async (userId: number, status: 'ACTIVE' | 'BANNED' | 'SUSPENDED') => {
    try {
      checkAdminPermission()
      
      const user = await request.patch<User>(`/api/admin/users/${userId}/status`, { status })
      
      // 更新本地状态
      const index = users.value.findIndex(u => u.id === userId)
      if (index !== -1) {
        users.value[index] = user
      }
      
      const statusTexts = {
        'ACTIVE': '激活',
        'BANNED': '封禁',
        'SUSPENDED': '暂停'
      }
      
      toast.add({
        title: `用户已${statusTexts[status]}`,
        color: 'green'
      })
      
      return user
    } catch (error: any) {
      console.error('更新用户状态失败:', error)
      toast.add({
        title: '操作失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 更新用户角色
  const updateUserRole = async (userId: number, role: 'USER' | 'MERCHANT' | 'ADMIN') => {
    try {
      checkAdminPermission()
      
      const user = await request.patch<User>(`/api/admin/users/${userId}/role`, { role })
      
      // 更新本地状态
      const index = users.value.findIndex(u => u.id === userId)
      if (index !== -1) {
        users.value[index] = user
      }
      
      toast.add({
        title: '用户角色已更新',
        color: 'green'
      })
      
      return user
    } catch (error: any) {
      console.error('更新用户角色失败:', error)
      toast.add({
        title: '操作失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 获取所有商品列表
  const fetchAllProducts = async (params: Record<string, any> = {}) => {
    try {
      checkAdminPermission()
      isLoading.value = true
      
      const response = await request.get<PaginatedResponse<Product>>('/api/admin/products', {
        params: {
          page: productsPagination.value.page,
          pageSize: productsPagination.value.pageSize,
          ...params
        }
      })
      
      products.value = response.items
      productsPagination.value = {
        page: response.page,
        pageSize: response.pageSize,
        total: response.total,
        totalPages: response.totalPages
      }
    } catch (error: any) {
      console.error('获取商品列表失败:', error)
      toast.add({
        title: '获取商品列表失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
    } finally {
      isLoading.value = false
    }
  }

  // 审核商品
  const reviewProduct = async (productId: number, action: 'APPROVE' | 'REJECT', reason?: string) => {
    try {
      checkAdminPermission()
      
      const product = await request.patch<Product>(`/api/admin/products/${productId}/review`, {
        action,
        reason
      })
      
      // 更新本地状态
      const index = products.value.findIndex(p => p.id === productId)
      if (index !== -1) {
        products.value[index] = product
      }
      
      toast.add({
        title: action === 'APPROVE' ? '商品已通过审核' : '商品已拒绝',
        color: 'green'
      })
      
      return product
    } catch (error: any) {
      console.error('审核商品失败:', error)
      toast.add({
        title: '操作失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 获取所有订单列表
  const fetchAllOrders = async (params: Record<string, any> = {}) => {
    try {
      checkAdminPermission()
      isLoading.value = true
      
      const response = await request.get<PaginatedResponse<Order>>('/api/admin/orders', {
        params: {
          page: ordersPagination.value.page,
          pageSize: ordersPagination.value.pageSize,
          ...params
        }
      })
      
      orders.value = response.items
      ordersPagination.value = {
        page: response.page,
        pageSize: response.pageSize,
        total: response.total,
        totalPages: response.totalPages
      }
    } catch (error: any) {
      console.error('获取订单列表失败:', error)
      toast.add({
        title: '获取订单列表失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
    } finally {
      isLoading.value = false
    }
  }

  // 处理退款申请
  const processRefund = async (orderId: number, action: 'APPROVE' | 'REJECT', reason?: string) => {
    try {
      checkAdminPermission()
      
      const order = await request.patch<Order>(`/api/admin/orders/${orderId}/refund`, {
        action,
        reason
      })
      
      // 更新本地状态
      const index = orders.value.findIndex(o => o.id === orderId)
      if (index !== -1) {
        orders.value[index] = order
      }
      
      toast.add({
        title: action === 'APPROVE' ? '退款已批准' : '退款已拒绝',
        color: 'green'
      })
      
      return order
    } catch (error: any) {
      console.error('处理退款失败:', error)
      toast.add({
        title: '操作失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 发送系统通知
  const sendSystemNotification = async (data: {
    userIds?: number[]
    title: string
    content: string
    type?: string
    actionUrl?: string
  }) => {
    try {
      checkAdminPermission()
      
      await request.post('/api/admin/notifications', data)
      
      toast.add({
        title: '系统通知已发送',
        color: 'green'
      })
    } catch (error: any) {
      console.error('发送系统通知失败:', error)
      toast.add({
        title: '发送失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 获取系统日志
  const fetchSystemLogs = async (params: Record<string, any> = {}) => {
    try {
      checkAdminPermission()
      
      const logs = await request.get('/api/admin/logs', { params })
      return logs
    } catch (error: any) {
      console.error('获取系统日志失败:', error)
      throw error
    }
  }

  // 导出数据
  const exportData = async (type: 'users' | 'products' | 'orders', params?: Record<string, any>) => {
    try {
      checkAdminPermission()
      
      const response = await request.get(`/api/admin/export/${type}`, {
        params,
        responseType: 'blob'
      })
      
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response]))
      const link = document.createElement('a')
      link.href = url
      link.download = `${type}_${new Date().toISOString().split('T')[0]}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      toast.add({
        title: '导出成功',
        color: 'green'
      })
    } catch (error: any) {
      console.error('导出数据失败:', error)
      toast.add({
        title: '导出失败',
        description: error.message || '请稍后重试',
        color: 'red'
      })
      throw error
    }
  }

  // 计算属性
  const isAdmin = computed(() => authStore.hasPermission('ADMIN'))
  const bannedUsers = computed(() => users.value.filter(user => user.status === 'BANNED'))
  const pendingProducts = computed(() => products.value.filter(product => product.status === 'PENDING'))
  const refundOrders = computed(() => orders.value.filter(order => order.refundStatus === 'PENDING'))

  // 清空状态
  const clearState = () => {
    users.value = []
    products.value = []
    orders.value = []
    statistics.value = {
      totalUsers: 0,
      totalProducts: 0,
      totalOrders: 0,
      totalRevenue: 0,
      todayUsers: 0,
      todayOrders: 0,
      todayRevenue: 0,
      activeUsers: 0,
      pendingOrders: 0,
      reportedPosts: 0
    }
    usersPagination.value = { page: 1, pageSize: 20, total: 0, totalPages: 0 }
    productsPagination.value = { page: 1, pageSize: 20, total: 0, totalPages: 0 }
    ordersPagination.value = { page: 1, pageSize: 20, total: 0, totalPages: 0 }
  }

  return {
    // 状态
    users: readonly(users),
    products: readonly(products),
    orders: readonly(orders),
    statistics: readonly(statistics),
    isLoading: readonly(isLoading),
    usersPagination: readonly(usersPagination),
    productsPagination: readonly(productsPagination),
    ordersPagination: readonly(ordersPagination),
    
    // 方法
    fetchStatistics,
    fetchUsers,
    updateUserStatus,
    updateUserRole,
    fetchAllProducts,
    reviewProduct,
    fetchAllOrders,
    processRefund,
    sendSystemNotification,
    fetchSystemLogs,
    exportData,
    
    // 计算属性
    isAdmin,
    bannedUsers,
    pendingProducts,
    refundOrders,
    
    // 工具方法
    clearState
  }
})
