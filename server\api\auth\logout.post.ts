/**
 * 用户登出接口
 * POST /api/auth/logout
 */

export default defineApiHandler(async event => {
  // 只允许POST请求
  assertMethod(event, 'POST')

  // 获取当前用户信息
  const userId = event.context.user?.id
  if (!userId) {
    throw new AuthenticationError('请先登录')
  }

  try {
    // 获取token
    const token = extractTokenFromHeader(event)

    // 记录登出日志
    await logLogout(userId, event, {
      logoutMethod: 'manual'
    })

    // 在实际项目中，这里可以：
    // 1. 将token加入黑名单（如果使用Redis等缓存）
    // 2. 记录登出时间到数据库
    // 3. 清除服务端session（如果使用session）

    return createSuccessResponse(null, '登出成功')
  } catch (error) {
    console.error('登出处理失败:', error)
    throw new InternalServerError('登出处理失败')
  }
})
