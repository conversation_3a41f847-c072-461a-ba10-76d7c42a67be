# 开发指南

本文档提供了社交购物网站项目的详细开发指南。

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- PostgreSQL >= 13.0
- Redis >= 6.0
- Docker & Docker Compose (可选，用于本地开发环境)

### 一键设置开发环境

```bash
# Linux/macOS
npm run dev:setup

# Windows
npm run dev:setup:win
```

### 手动设置

1. **克隆项目**
```bash
git clone <repository-url>
cd social-shop
```

2. **安装依赖**
```bash
npm install
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

4. **启动开发服务**
```bash
# 启动数据库和Redis (使用Docker)
npm run dev:services

# 运行数据库迁移
npm run db:migrate

# 填充测试数据
npm run db:seed

# 启动开发服务器
npm run dev
```

## 📁 项目结构

```
social-shop/
├── assets/              # 静态资源
├── components/          # Vue组件
├── layouts/            # 布局组件
├── pages/              # 页面路由
├── server/             # 服务端代码
│   ├── api/            # API路由
│   └── middleware/     # 服务端中间件
├── stores/             # Pinia状态管理
├── types/              # TypeScript类型定义
├── utils/              # 工具函数
├── prisma/             # 数据库模型和迁移
├── docker/             # Docker配置
├── scripts/            # 开发脚本
└── docs/               # 项目文档
```

## 🛠️ 开发工具

### 代码规范

项目使用以下工具确保代码质量：

- **ESLint**: 代码检查
- **Prettier**: 代码格式化
- **EditorConfig**: 编辑器配置
- **TypeScript**: 类型检查

### Git工作流

项目配置了Git hooks来自动化代码质量检查：

- **pre-commit**: 运行lint-staged，自动格式化和检查代码
- **commit-msg**: 验证提交信息格式

### 提交信息规范

使用Conventional Commits规范：

```
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

类型说明：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `perf`: 性能优化
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(auth): 添加用户登录功能

- 实现JWT身份认证
- 添加登录表单验证
- 集成Redis会话存储

Closes #123
```

## 🔧 开发命令

### 基础命令

```bash
# 开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview

# 类型检查
npm run type-check
```

### 代码质量

```bash
# 代码检查
npm run lint

# 自动修复代码问题
npm run lint:fix

# 代码格式化
npm run format

# 检查代码格式
npm run format:check
```

### 数据库操作

```bash
# 生成Prisma客户端
npm run db:generate

# 运行数据库迁移
npm run db:migrate

# 填充测试数据
npm run db:seed

# 打开Prisma Studio
npm run db:studio

# 重置数据库
npm run db:reset
```

### Docker服务

```bash
# 启动开发服务
npm run dev:services

# 停止开发服务
npm run dev:services:stop

# 查看服务日志
npm run dev:services:logs
```

## 🗄️ 数据库开发

### Prisma工作流

1. **修改schema**
```bash
# 编辑 prisma/schema.prisma
```

2. **创建迁移**
```bash
npm run db:migrate
```

3. **生成客户端**
```bash
npm run db:generate
```

### 数据库查询示例

```typescript
// 获取用户信息
const user = await prisma.user.findUnique({
  where: { id: userId },
  include: {
    posts: true,
    followers: true
  }
})

// 创建商品
const product = await prisma.product.create({
  data: {
    name: '商品名称',
    price: 99.99,
    categoryId: 1,
    merchantId: userId
  }
})
```

## 🎨 前端开发

### 组件开发规范

1. **组件命名**: 使用PascalCase
2. **文件结构**: 每个组件一个文件夹
3. **Props定义**: 使用TypeScript接口
4. **样式**: 使用Tailwind CSS

### 组件示例

```vue
<script setup lang="ts">
interface Props {
  title: string
  description?: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  click: [id: number]
}>()
</script>

<template>
  <div class="card">
    <h2 class="text-xl font-bold">{{ props.title }}</h2>
    <p v-if="props.description" class="text-gray-600">
      {{ props.description }}
    </p>
  </div>
</template>
```

### 状态管理

使用Pinia进行状态管理：

```typescript
// stores/user.ts
export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  
  const login = async (credentials: LoginForm) => {
    const response = await $fetch('/api/auth/login', {
      method: 'POST',
      body: credentials
    })
    user.value = response.data.user
  }
  
  return { user, login }
})
```

## 🔌 API开发

### 服务端API规范

1. **RESTful设计**: 遵循REST API设计原则
2. **错误处理**: 统一的错误响应格式
3. **验证**: 使用Zod进行请求验证
4. **认证**: JWT token验证

### API示例

```typescript
// server/api/products/index.get.ts
export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const { page = 1, pageSize = 10 } = query
  
  const products = await prisma.product.findMany({
    skip: (page - 1) * pageSize,
    take: pageSize,
    include: {
      category: true,
      merchant: true
    }
  })
  
  return {
    success: true,
    data: products
  }
})
```

## 🧪 测试

### 测试策略

1. **单元测试**: 测试工具函数和组件
2. **集成测试**: 测试API接口
3. **E2E测试**: 测试完整用户流程

### 测试命令

```bash
# 运行测试
npm run test

# 监听模式
npm run test:watch

# 覆盖率报告
npm run test:coverage
```

## 📱 响应式设计

使用Tailwind CSS断点：

- `sm`: 640px+
- `md`: 768px+
- `lg`: 1024px+
- `xl`: 1280px+
- `2xl`: 1536px+

## 🚀 部署

### 构建优化

```bash
# 生产构建
npm run build

# 分析构建包
npm run analyze
```

### 环境变量

生产环境需要配置的环境变量：

```env
DATABASE_URL=postgresql://...
REDIS_URL=redis://...
JWT_SECRET=your-secret-key
NUXT_PUBLIC_API_BASE=/api
```

## 🔍 调试

### VSCode调试

项目已配置VSCode调试：

1. 按F5启动调试
2. 选择"Nuxt: 调试全栈"
3. 在代码中设置断点

### 日志记录

```typescript
// 开发环境
console.log('调试信息')

// 生产环境
const logger = useLogger()
logger.info('应用信息')
logger.error('错误信息')
```

## 📚 学习资源

- [Nuxt.js文档](https://nuxt.com/)
- [Vue.js文档](https://vuejs.org/)
- [Prisma文档](https://www.prisma.io/docs)
- [Tailwind CSS文档](https://tailwindcss.com/)
- [TypeScript文档](https://www.typescriptlang.org/)

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支: `git checkout -b feature/amazing-feature`
3. 提交更改: `git commit -m 'feat: add amazing feature'`
4. 推送分支: `git push origin feature/amazing-feature`
5. 创建Pull Request

## ❓ 常见问题

### Q: 如何重置开发环境？

```bash
# 停止所有服务
npm run dev:services:stop

# 清理缓存
npm run dev:clean

# 重新设置
npm run dev:setup
```

### Q: 数据库连接失败？

检查PostgreSQL服务是否启动，确认.env文件中的数据库配置正确。

### Q: 端口冲突？

修改nuxt.config.ts中的devServer.port配置，或者停止占用端口的其他服务。
