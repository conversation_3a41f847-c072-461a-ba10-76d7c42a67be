#!/bin/bash

# 数据库初始化脚本
echo "🚀 开始初始化数据库..."

# 检查是否安装了 Prisma CLI
if ! command -v prisma &> /dev/null; then
    echo "❌ Prisma CLI 未安装，请先运行: npm install -g prisma"
    exit 1
fi

# 生成 Prisma Client
echo "📦 生成 Prisma Client..."
npx prisma generate

# 推送数据库架构
echo "🗄️ 推送数据库架构..."
npx prisma db push

# 运行种子数据
echo "🌱 填充种子数据..."
npx prisma db seed

echo "✅ 数据库初始化完成！"
echo ""
echo "📋 测试账号信息："
echo "管理员: <EMAIL> / 123456"
echo "商家: <EMAIL> / 123456"
echo "用户: <EMAIL> / 123456"
echo ""
echo "🎫 已为测试用户创建了优惠券"
echo "❤️ 收藏功能已就绪"
echo ""
echo "🌐 现在可以启动应用: npm run dev"
