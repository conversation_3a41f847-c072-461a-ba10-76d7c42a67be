/**
 * 日志记录工具函数
 */

import type { H3Event } from 'h3'

// 获取客户端IP地址
export function getClientIP(event: H3Event): string | null {
  const headers = getHeaders(event)
  
  // 检查各种可能的IP头
  const ipHeaders = [
    'x-forwarded-for',
    'x-real-ip',
    'x-client-ip',
    'cf-connecting-ip', // Cloudflare
    'x-forwarded',
    'forwarded-for',
    'forwarded'
  ]
  
  for (const header of ipHeaders) {
    const value = headers[header]
    if (value) {
      // x-forwarded-for 可能包含多个IP，取第一个
      const ip = Array.isArray(value) ? value[0] : value
      const firstIP = ip.split(',')[0].trim()
      if (firstIP && firstIP !== 'unknown') {
        return firstIP
      }
    }
  }
  
  // 回退到连接的远程地址
  return event.node.req.socket?.remoteAddress || null
}

// 获取用户代理
export function getUserAgent(event: H3Event): string | null {
  const headers = getHeaders(event)
  return headers['user-agent'] || null
}

// 记录搜索日志
export async function logSearch(
  keyword: string,
  resultCount: number,
  filters: any = {},
  event: H3Event,
  userId?: number
) {
  try {
    const ipAddress = getClientIP(event)
    const userAgent = getUserAgent(event)
    
    await prisma.searchLog.create({
      data: {
        keyword: keyword.trim(),
        resultCount,
        userId,
        userAgent,
        ipAddress,
        filters: Object.keys(filters).length > 0 ? filters : null
      }
    })
    
    console.log(`[SEARCH] 用户${userId || '未登录'} 搜索 "${keyword}" 找到 ${resultCount} 个结果`)
  } catch (error) {
    console.error('记录搜索日志失败:', error)
  }
}

// 记录用户操作日志
export async function logUserAction(
  action: string,
  event: H3Event,
  options: {
    userId?: number
    resource?: string
    resourceId?: number
    details?: any
  } = {}
) {
  try {
    const ipAddress = getClientIP(event)
    const userAgent = getUserAgent(event)
    
    await prisma.userLog.create({
      data: {
        userId: options.userId,
        action: action as any,
        resource: options.resource,
        resourceId: options.resourceId,
        details: options.details ? JSON.parse(JSON.stringify(options.details)) : null,
        userAgent,
        ipAddress
      }
    })
    
    const userInfo = options.userId ? `用户${options.userId}` : '未登录用户'
    const resourceInfo = options.resource && options.resourceId 
      ? ` 资源${options.resource}:${options.resourceId}` 
      : ''
    
    console.log(`[USER_LOG] ${userInfo} 执行 ${action}${resourceInfo}`)
  } catch (error) {
    console.error('记录用户操作日志失败:', error)
  }
}

// 记录登录日志
export async function logLogin(
  userId: number,
  event: H3Event,
  details: {
    loginMethod?: string // 登录方式：email, phone, oauth等
    success: boolean
    failureReason?: string
  }
) {
  try {
    await logUserAction('LOGIN', event, {
      userId: details.success ? userId : undefined,
      details: {
        loginMethod: details.loginMethod || 'email',
        success: details.success,
        failureReason: details.failureReason,
        timestamp: new Date().toISOString()
      }
    })
  } catch (error) {
    console.error('记录登录日志失败:', error)
  }
}

// 记录登出日志
export async function logLogout(
  userId: number,
  event: H3Event,
  details: {
    logoutMethod?: string // 登出方式：manual, timeout, force等
  } = {}
) {
  try {
    await logUserAction('LOGOUT', event, {
      userId,
      details: {
        logoutMethod: details.logoutMethod || 'manual',
        timestamp: new Date().toISOString()
      }
    })
  } catch (error) {
    console.error('记录登出日志失败:', error)
  }
}

// 记录注册日志
export async function logRegister(
  userId: number,
  event: H3Event,
  details: {
    registerMethod?: string // 注册方式：email, phone, oauth等
    referrer?: string // 推荐人
  } = {}
) {
  try {
    await logUserAction('REGISTER', event, {
      userId,
      details: {
        registerMethod: details.registerMethod || 'email',
        referrer: details.referrer,
        timestamp: new Date().toISOString()
      }
    })
  } catch (error) {
    console.error('记录注册日志失败:', error)
  }
}

// 记录商品查看日志
export async function logProductView(
  productId: number,
  event: H3Event,
  userId?: number
) {
  try {
    await logUserAction('PRODUCT_VIEW', event, {
      userId,
      resource: 'product',
      resourceId: productId,
      details: {
        timestamp: new Date().toISOString()
      }
    })
  } catch (error) {
    console.error('记录商品查看日志失败:', error)
  }
}

// 记录订单操作日志
export async function logOrderAction(
  action: 'ORDER_CREATE' | 'ORDER_PAY' | 'ORDER_CANCEL',
  orderId: number,
  event: H3Event,
  userId: number,
  details: any = {}
) {
  try {
    await logUserAction(action, event, {
      userId,
      resource: 'order',
      resourceId: orderId,
      details: {
        ...details,
        timestamp: new Date().toISOString()
      }
    })
  } catch (error) {
    console.error('记录订单操作日志失败:', error)
  }
}

// 记录购物车操作日志
export async function logCartAction(
  action: 'CART_ADD' | 'CART_REMOVE',
  productId: number,
  event: H3Event,
  userId: number,
  details: any = {}
) {
  try {
    await logUserAction(action, event, {
      userId,
      resource: 'product',
      resourceId: productId,
      details: {
        ...details,
        timestamp: new Date().toISOString()
      }
    })
  } catch (error) {
    console.error('记录购物车操作日志失败:', error)
  }
}

// 记录收藏操作日志
export async function logFavoriteAction(
  action: 'FAVORITE_ADD' | 'FAVORITE_REMOVE',
  productId: number,
  event: H3Event,
  userId: number
) {
  try {
    await logUserAction(action, event, {
      userId,
      resource: 'product',
      resourceId: productId,
      details: {
        timestamp: new Date().toISOString()
      }
    })
  } catch (error) {
    console.error('记录收藏操作日志失败:', error)
  }
}

// 批量清理过期日志
export async function cleanupOldLogs(daysToKeep: number = 90) {
  try {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)
    
    const [searchLogsDeleted, userLogsDeleted] = await Promise.all([
      prisma.searchLog.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate
          }
        }
      }),
      prisma.userLog.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate
          }
        }
      })
    ])
    
    console.log(`清理日志完成: 搜索日志 ${searchLogsDeleted.count} 条, 用户日志 ${userLogsDeleted.count} 条`)
    
    return {
      searchLogsDeleted: searchLogsDeleted.count,
      userLogsDeleted: userLogsDeleted.count
    }
  } catch (error) {
    console.error('清理日志失败:', error)
    throw error
  }
}
