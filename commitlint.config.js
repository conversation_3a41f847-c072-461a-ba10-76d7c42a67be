module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    // 类型枚举
    'type-enum': [
      2,
      'always',
      [
        'feat',     // 新功能
        'fix',      // 修复bug
        'docs',     // 文档更新
        'style',    // 代码格式调整（不影响代码运行的变动）
        'refactor', // 重构（既不是新增功能，也不是修改bug的代码变动）
        'perf',     // 性能优化
        'test',     // 增加测试
        'chore',    // 构建过程或辅助工具的变动
        'revert',   // 回滚
        'build',    // 构建系统或外部依赖项的更改
        'ci'        // CI配置文件和脚本的更改
      ]
    ],
    // 主题不能为空
    'subject-empty': [2, 'never'],
    // 主题长度限制
    'subject-max-length': [2, 'always', 50],
    // 主题格式（不以大写字母开头，不以句号结尾）
    'subject-case': [2, 'always', 'lower-case'],
    'subject-full-stop': [2, 'never', '.'],
    // 类型不能为空
    'type-empty': [2, 'never'],
    // 类型格式
    'type-case': [2, 'always', 'lower-case'],
    // 头部最大长度
    'header-max-length': [2, 'always', 72],
    // 正文前必须有空行
    'body-leading-blank': [2, 'always'],
    // 脚注前必须有空行
    'footer-leading-blank': [2, 'always']
  },
  // 自定义解析器选项
  parserPreset: {
    parserOpts: {
      // 支持中文提交信息
      headerPattern: /^(\w*)(?:\((.*)\))?: (.*)$/,
      headerCorrespondence: ['type', 'scope', 'subject']
    }
  },
  // 忽略规则（用于某些特殊情况）
  ignores: [
    (commit) => commit.includes('WIP'), // 忽略包含WIP的提交
    (commit) => commit.includes('Merge') // 忽略合并提交
  ],
  // 默认忽略规则
  defaultIgnores: true,
  // 帮助信息
  helpUrl: 'https://github.com/conventional-changelog/commitlint/#what-is-commitlint'
}
