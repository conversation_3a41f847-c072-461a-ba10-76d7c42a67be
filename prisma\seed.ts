import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

// 优惠券种子数据函数
async function seedCoupons() {
  console.log('开始创建优惠券数据...')

  const coupons = [
    {
      name: '新用户专享券',
      description: '新用户注册专享，满100元减20元',
      type: 'SHARED' as const,
      discountType: 'FIXED' as const,
      discountValue: 20,
      minOrderAmount: 100,
      totalCount: 10000,
      perUserLimit: 1,
      applicableScope: 'ALL' as const,
      applicableIds: [],
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-12-31'),
      status: 'ACTIVE' as const
    },
    {
      name: '全场8折券',
      description: '全场商品8折优惠，最高优惠50元',
      type: 'EXCLUSIVE' as const,
      discountType: 'PERCENTAGE' as const,
      discountValue: 20,
      maxDiscountAmount: 50,
      minOrderAmount: 200,
      totalCount: 5000,
      perUserLimit: 2,
      applicableScope: 'ALL' as const,
      applicableIds: [],
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-12-31'),
      status: 'ACTIVE' as const
    },
    {
      name: '满300减50',
      description: '全场满300元减50元',
      type: 'SHARED' as const,
      discountType: 'FIXED' as const,
      discountValue: 50,
      minOrderAmount: 300,
      totalCount: 8000,
      perUserLimit: 3,
      applicableScope: 'ALL' as const,
      applicableIds: [],
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-12-31'),
      status: 'ACTIVE' as const
    }
  ]

  for (const couponData of coupons) {
    await prisma.coupon.create({
      data: couponData
    })
  }

  console.log(`✅ 成功创建 ${coupons.length} 个优惠券`)
}

async function main() {
  console.log('开始填充数据库...')

  // 创建用户
  const hashedPassword = await bcrypt.hash('123456', 12)

  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      nickname: '管理员',
      role: 'ADMIN',
      status: 'ACTIVE'
    }
  })

  const merchantUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      username: 'merchant',
      email: '<EMAIL>',
      password: hashedPassword,
      nickname: '商家用户',
      role: 'MERCHANT',
      status: 'ACTIVE'
    }
  })

  const normalUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      username: 'testuser',
      email: '<EMAIL>',
      password: hashedPassword,
      nickname: '测试用户',
      role: 'USER',
      status: 'ACTIVE'
    }
  })

  console.log('用户创建完成')

  // 创建商品分类
  const electronicsCategory = await prisma.category.upsert({
    where: { slug: 'electronics' },
    update: {},
    create: {
      name: '数码电子',
      slug: 'electronics',
      description: '手机、电脑、数码配件等',
      status: 'ACTIVE',
      sort: 1
    }
  })

  const clothingCategory = await prisma.category.upsert({
    where: { slug: 'clothing' },
    update: {},
    create: {
      name: '服装鞋帽',
      slug: 'clothing',
      description: '男装、女装、鞋子、配饰等',
      status: 'ACTIVE',
      sort: 2
    }
  })

  const homeCategory = await prisma.category.upsert({
    where: { slug: 'home' },
    update: {},
    create: {
      name: '家居生活',
      slug: 'home',
      description: '家具、家电、日用品等',
      status: 'ACTIVE',
      sort: 3
    }
  })

  console.log('商品分类创建完成')

  // 创建商品
  const products = [
    {
      name: 'iPhone 15 Pro',
      description: '苹果最新旗舰手机，搭载A17 Pro芯片，支持5G网络',
      price: 7999.0,
      originalPrice: 8999.0,
      images: ['/images/products/iphone15pro.jpg'],
      categoryId: electronicsCategory.id,
      merchantId: merchantUser.id,
      stock: 100,
      tags: ['苹果', '5G', '旗舰'],
      specifications: {
        屏幕尺寸: '6.1英寸',
        存储容量: '128GB',
        颜色: '深空黑色',
        网络: '5G'
      }
    },
    {
      name: '时尚休闲T恤',
      description: '100%纯棉材质，舒适透气，多色可选',
      price: 99.0,
      originalPrice: 129.0,
      images: ['/images/products/tshirt.jpg'],
      categoryId: clothingCategory.id,
      merchantId: merchantUser.id,
      stock: 200,
      tags: ['纯棉', '休闲', '舒适'],
      specifications: {
        材质: '100%纯棉',
        尺码: 'M',
        颜色: '白色',
        版型: '宽松'
      }
    },
    {
      name: '智能扫地机器人',
      description: '全自动清扫，智能规划路径，支持APP控制',
      price: 1299.0,
      images: ['/images/products/robot-vacuum.jpg'],
      categoryId: homeCategory.id,
      merchantId: merchantUser.id,
      stock: 50,
      tags: ['智能', '清洁', '自动'],
      specifications: {
        清扫面积: '150平米',
        电池续航: '120分钟',
        噪音: '≤60dB',
        连接方式: 'WiFi'
      }
    }
  ]

  for (const productData of products) {
    await prisma.product.upsert({
      where: {
        name: productData.name
      },
      update: {},
      create: productData
    })
  }

  console.log('商品创建完成')

  // 创建收货地址
  await prisma.shippingAddress.upsert({
    where: {
      id: 1
    },
    update: {},
    create: {
      userId: normalUser.id,
      name: '张三',
      phone: '13800138000',
      province: '北京市',
      city: '北京市',
      district: '朝阳区',
      address: '三里屯街道100号',
      postalCode: '100000',
      isDefault: true
    }
  })

  console.log('收货地址创建完成')

  // 创建社交动态
  const posts = [
    {
      userId: normalUser.id,
      content: '刚收到的新手机，拍照效果真的很棒！推荐给大家～',
      type: 'TEXT' as const,
      status: 'PUBLISHED' as const
    },
    {
      userId: merchantUser.id,
      content: '新品上架啦！这款T恤质量超好，欢迎大家选购！',
      type: 'TEXT' as const,
      status: 'PUBLISHED' as const
    }
  ]

  for (const postData of posts) {
    await prisma.post.create({
      data: postData
    })
  }

  console.log('社交动态创建完成')

  // 创建关注关系
  await prisma.follow.upsert({
    where: {
      followerId_followingId: {
        followerId: normalUser.id,
        followingId: merchantUser.id
      }
    },
    update: {},
    create: {
      followerId: normalUser.id,
      followingId: merchantUser.id
    }
  })

  console.log('关注关系创建完成')

  // 创建优惠券数据
  await seedCoupons()

  // 为测试用户创建一些优惠券
  const coupons = await prisma.coupon.findMany({ take: 3 })
  for (const coupon of coupons) {
    await prisma.userCoupon.create({
      data: {
        userId: normalUser.id,
        couponId: coupon.id,
        expiresAt: coupon.endDate,
        status: 'UNUSED'
      }
    })
  }

  console.log('用户优惠券创建完成')

  console.log('数据库填充完成！')
  console.log('测试账号:')
  console.log('管理员: <EMAIL> / 123456')
  console.log('商家: <EMAIL> / 123456')
  console.log('用户: <EMAIL> / 123456')
}

main()
  .catch(e => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
