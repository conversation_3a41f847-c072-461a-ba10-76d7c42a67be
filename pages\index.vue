<template>
  <div>
    <!-- 首页轮播图 -->
    <section class="hero-section">
      <div class="container mx-auto px-4 py-16">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">欢迎来到社交购物网站</h1>
          <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">发现好物，分享生活，与朋友一起享受购物的乐趣</p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <UButton size="lg" @click="navigateTo('/products')" class="px-8 py-3"> 开始购物 </UButton>
            <UButton variant="outline" size="lg" @click="navigateTo('/social')" class="px-8 py-3"> 探索社区 </UButton>
          </div>
        </div>
      </div>
    </section>

    <!-- 热门商品 -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">热门商品</h2>
          <p class="text-gray-600">精选优质商品，为您推荐</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div
            v-for="product in featuredProducts"
            :key="product.id"
            class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
          >
            <img :src="product.image" :alt="product.name" class="w-full h-48 object-cover" />
            <div class="p-4">
              <h3 class="font-semibold text-gray-900 mb-2">{{ product.name }}</h3>
              <p class="text-gray-600 text-sm mb-3">{{ product.description }}</p>
              <div class="flex justify-between items-center">
                <span class="text-lg font-bold text-primary-600"> ¥{{ product.price }} </span>
                <UButton size="sm" @click="addToCart(product)"> 加入购物车 </UButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 社交动态 -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">社区动态</h2>
          <p class="text-gray-600">看看大家都在分享什么</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="post in socialPosts" :key="post.id" class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center mb-4">
              <img :src="post.user.avatar" :alt="post.user.name" class="w-10 h-10 rounded-full mr-3" />
              <div>
                <h4 class="font-semibold text-gray-900">{{ post.user.name }}</h4>
                <p class="text-sm text-gray-500">{{ formatDate(post.createdAt) }}</p>
              </div>
            </div>
            <p class="text-gray-700 mb-4">{{ post.content }}</p>
            <div class="flex items-center justify-between text-sm text-gray-500">
              <span>{{ post.likes }} 点赞</span>
              <span>{{ post.comments }} 评论</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// 页面元信息
definePageMeta({
  layout: 'default'
})

// 模拟数据
const featuredProducts = ref([
  {
    id: 1,
    name: '时尚T恤',
    description: '舒适透气，多色可选',
    price: 99,
    image: '/images/products/tshirt.jpg'
  },
  {
    id: 2,
    name: '运动鞋',
    description: '轻便舒适，适合运动',
    price: 299,
    image: '/images/products/shoes.jpg'
  },
  {
    id: 3,
    name: '手机壳',
    description: '防摔保护，时尚美观',
    price: 39,
    image: '/images/products/phone-case.jpg'
  },
  {
    id: 4,
    name: '蓝牙耳机',
    description: '高音质，长续航',
    price: 199,
    image: '/images/products/earphones.jpg'
  }
])

const socialPosts = ref([
  {
    id: 1,
    user: {
      name: '小明',
      avatar: '/images/avatars/user1.jpg'
    },
    content: '刚收到的新T恤，质量很不错！推荐给大家～',
    likes: 15,
    comments: 3,
    createdAt: new Date('2024-01-15')
  },
  {
    id: 2,
    user: {
      name: '小红',
      avatar: '/images/avatars/user2.jpg'
    },
    content: '这双运动鞋穿着超舒服，跑步必备！',
    likes: 28,
    comments: 7,
    createdAt: new Date('2024-01-14')
  },
  {
    id: 3,
    user: {
      name: '小李',
      avatar: '/images/avatars/user3.jpg'
    },
    content: '分享一下我的购物心得，大家一起来讨论～',
    likes: 42,
    comments: 12,
    createdAt: new Date('2024-01-13')
  }
])

// 状态管理
const authStore = useAuthStore()
const cartStore = useCartStore()
const toast = useToast()

// 方法
const addToCart = async (product: any) => {
  if (!authStore.isLoggedIn) {
    toast.add({
      title: '请先登录',
      description: '登录后即可添加到购物车',
      color: 'yellow'
    })
    return navigateTo('/login')
  }

  try {
    await cartStore.addItem(product.id, 1)
  } catch (error) {
    console.error('添加到购物车失败:', error)
  }
}

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('zh-CN', {
    month: 'short',
    day: 'numeric'
  }).format(date)
}
</script>

<style scoped>
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.container {
  max-width: 1200px;
}
</style>
