# 📊 日志系统文档

本文档介绍项目中的日志记录系统，包括搜索日志、用户操作日志的记录、查询和管理。

## 🎯 功能概述

### 日志类型

1. **搜索日志 (SearchLog)**
   - 记录用户的搜索行为
   - 包含搜索关键词、结果数量、筛选条件等
   - 用于分析用户搜索习惯和优化搜索功能

2. **用户操作日志 (UserLog)**
   - 记录用户的重要操作行为
   - 包含登录登出、商品操作、订单操作等
   - 用于安全审计和用户行为分析

## 📋 数据表结构

### 搜索日志表 (search_logs)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | SERIAL | 主键 |
| keyword | VARCHAR(255) | 搜索关键词 |
| result_count | INTEGER | 搜索结果数量 |
| user_id | INTEGER | 用户ID（可为空） |
| user_agent | TEXT | 用户代理字符串 |
| ip_address | VARCHAR(45) | IP地址 |
| filters | JSONB | 搜索筛选条件 |
| created_at | TIMESTAMP | 创建时间 |

### 用户操作日志表 (user_logs)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | SERIAL | 主键 |
| user_id | INTEGER | 用户ID（可为空） |
| action | UserLogAction | 操作类型 |
| resource | VARCHAR(100) | 资源类型 |
| resource_id | INTEGER | 资源ID |
| details | JSONB | 操作详情 |
| user_agent | TEXT | 用户代理字符串 |
| ip_address | VARCHAR(45) | IP地址 |
| created_at | TIMESTAMP | 创建时间 |

### 操作类型枚举 (UserLogAction)

- `LOGIN` - 登录
- `LOGOUT` - 登出
- `REGISTER` - 注册
- `PASSWORD_CHANGE` - 修改密码
- `PROFILE_UPDATE` - 更新个人信息
- `PRODUCT_VIEW` - 查看商品
- `PRODUCT_SEARCH` - 搜索商品
- `ORDER_CREATE` - 创建订单
- `ORDER_PAY` - 支付订单
- `ORDER_CANCEL` - 取消订单
- `CART_ADD` - 添加到购物车
- `CART_REMOVE` - 从购物车移除
- `FAVORITE_ADD` - 添加收藏
- `FAVORITE_REMOVE` - 移除收藏
- `REVIEW_CREATE` - 创建评价
- `COUPON_CLAIM` - 领取优惠券
- `COUPON_USE` - 使用优惠券
- `POST_CREATE` - 创建动态
- `POST_LIKE` - 点赞动态
- `COMMENT_CREATE` - 创建评论
- `FOLLOW_USER` - 关注用户
- `UNFOLLOW_USER` - 取消关注

## 🛠️ 使用方法

### 记录日志

#### 1. 搜索日志

```typescript
import { logSearch } from '~/server/utils/logger'

// 在搜索API中记录
await logSearch(keyword, resultCount, filters, event, userId)
```

#### 2. 用户操作日志

```typescript
import { logUserAction, logLogin, logLogout } from '~/server/utils/logger'

// 记录登录
await logLogin(userId, event, {
  loginMethod: 'email',
  success: true
})

// 记录登出
await logLogout(userId, event, {
  logoutMethod: 'manual'
})

// 记录通用操作
await logUserAction('PRODUCT_VIEW', event, {
  userId,
  resource: 'product',
  resourceId: productId
})
```

### 查询日志

#### 1. 搜索日志管理

```bash
# 获取搜索日志列表
GET /api/admin/logs/search?page=1&pageSize=20&keyword=手机

# 查询参数
- keyword: 搜索关键词过滤
- userId: 用户ID过滤
- startDate: 开始时间
- endDate: 结束时间
- minResults: 最小结果数
- maxResults: 最大结果数
```

#### 2. 用户操作日志管理

```bash
# 获取用户操作日志列表
GET /api/admin/logs/users?page=1&pageSize=20&action=LOGIN

# 查询参数
- userId: 用户ID过滤
- action: 操作类型过滤
- resource: 资源类型过滤
- resourceId: 资源ID过滤
- startDate: 开始时间
- endDate: 结束时间
- ipAddress: IP地址过滤
```

#### 3. 日志统计

```bash
# 获取日志统计信息
GET /api/admin/logs/stats?days=7

# 返回数据包括
- 搜索统计
- 用户活动统计
- 每日趋势
- 热门关键词
- 操作类型统计
- 活跃用户
- IP统计
```

### 日志清理

#### 1. 手动清理

```bash
# 清理过期日志
POST /api/admin/logs/cleanup
{
  "daysToKeep": 90,
  "logTypes": ["search", "user"]
}
```

#### 2. 定时清理

```bash
# 定时任务清理（需要配置CRON_SECRET）
POST /api/cron/cleanup-logs
Authorization: Bearer your-cron-secret
```

## ⚙️ 配置说明

### 环境变量

```env
# 日志保留天数（默认90天）
LOG_RETENTION_DAYS=90

# 定时任务密钥
CRON_SECRET=your-cron-secret-key-here
```

### 自动日志记录

系统通过中间件自动记录以下操作的日志：

- 用户登录/登出/注册
- 商品查看
- 购物车操作
- 订单操作
- 收藏操作
- 社交操作

## 📈 监控和分析

### 1. 搜索分析

- **热门关键词**: 分析用户搜索偏好
- **搜索结果质量**: 通过结果数量分析搜索效果
- **搜索趋势**: 了解搜索量变化

### 2. 用户行为分析

- **活跃用户**: 识别高活跃度用户
- **操作频率**: 分析各功能使用情况
- **用户路径**: 追踪用户操作流程

### 3. 安全监控

- **异常登录**: 监控异常IP或频繁登录
- **操作审计**: 追踪重要操作记录
- **风险识别**: 识别可疑行为模式

## 🔧 维护建议

### 1. 定期清理

- 建议保留90天的日志数据
- 设置定时任务自动清理过期日志
- 重要日志可以导出备份

### 2. 性能优化

- 为常用查询字段添加索引
- 定期分析慢查询
- 考虑日志表分区

### 3. 存储管理

- 监控日志表大小
- 及时清理无用数据
- 考虑使用专门的日志存储系统

## 🚨 注意事项

1. **隐私保护**: 不记录敏感信息如密码
2. **性能影响**: 日志记录异步进行，不影响主业务
3. **错误处理**: 日志记录失败不影响正常功能
4. **数据安全**: 定期备份重要日志数据
5. **合规要求**: 遵守数据保护法规要求

## 📞 故障排除

### 常见问题

1. **日志记录失败**
   - 检查数据库连接
   - 验证表结构是否正确
   - 查看错误日志

2. **查询性能慢**
   - 检查索引是否存在
   - 优化查询条件
   - 考虑数据量是否过大

3. **存储空间不足**
   - 清理过期日志
   - 调整保留策略
   - 扩展存储空间

### 调试方法

```bash
# 查看日志记录情况
console.log('日志记录:', { action, userId, resource })

# 检查数据库记录
SELECT COUNT(*) FROM search_logs WHERE created_at > NOW() - INTERVAL '1 day';
SELECT COUNT(*) FROM user_logs WHERE created_at > NOW() - INTERVAL '1 day';
```

---

**提示**: 日志系统是了解用户行为和系统运行状况的重要工具，合理使用可以大大提升产品质量和用户体验。
