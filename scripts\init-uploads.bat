@echo off
chcp 65001 >nul

echo 📁 创建上传目录...

REM 创建上传目录
if not exist "public\uploads\images" mkdir "public\uploads\images"
if not exist "public\uploads\avatars" mkdir "public\uploads\avatars"
if not exist "public\uploads\documents" mkdir "public\uploads\documents"

REM 创建.gitkeep文件以确保空目录被Git跟踪
echo. > "public\uploads\images\.gitkeep"
echo. > "public\uploads\avatars\.gitkeep"
echo. > "public\uploads\documents\.gitkeep"

echo.
echo ✅ 上传目录创建完成！
echo.
echo 📂 创建的目录：
echo   - public\uploads\images\     (图片上传)
echo   - public\uploads\avatars\    (头像上传)
echo   - public\uploads\documents\  (文档上传)
echo.
echo 📝 已创建 .gitkeep 文件确保目录被Git跟踪
echo.
pause
