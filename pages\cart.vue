<template>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold text-gray-900 mb-6">购物车</h1>
    
    <!-- 购物车为空 -->
    <div v-if="cartStore.isEmpty" class="text-center py-12">
      <Icon name="heroicons:shopping-cart" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
      <h2 class="text-xl font-semibold text-gray-600 mb-2">购物车是空的</h2>
      <p class="text-gray-500 mb-6">快去挑选您喜欢的商品吧！</p>
      <UButton @click="navigateTo('/products')">
        去购物
      </UButton>
    </div>

    <!-- 购物车商品列表 -->
    <div v-else class="space-y-6">
      <!-- 操作栏 -->
      <div class="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
        <div class="flex items-center space-x-4">
          <UCheckbox
            :model-value="cartStore.isAllSelected"
            @update:model-value="cartStore.toggleSelectAll"
            label="全选"
          />
          <span class="text-sm text-gray-600">
            共{{ cartStore.summary.totalItems }}件商品
          </span>
        </div>
        
        <div class="flex items-center space-x-4">
          <UButton
            variant="ghost"
            size="sm"
            @click="cartStore.removeSelected"
            :disabled="!cartStore.hasSelected"
          >
            删除选中
          </UButton>
          <UButton
            variant="ghost"
            size="sm"
            @click="handleClearCart"
          >
            清空购物车
          </UButton>
        </div>
      </div>

      <!-- 商品列表 -->
      <div class="space-y-4">
        <UCard
          v-for="item in cartStore.items"
          :key="item.id"
          class="p-4"
        >
          <div class="flex items-start space-x-4">
            <!-- 选择框 -->
            <UCheckbox
              :model-value="item.selected"
              @update:model-value="(selected) => cartStore.toggleSelected(item.id, selected)"
              class="mt-2"
            />

            <!-- 商品图片 -->
            <div class="flex-shrink-0">
              <NuxtLink :to="`/products/${item.product.id}`">
                <img 
                  :src="item.product.images[0] || '/images/placeholder.jpg'"
                  :alt="item.product.name"
                  class="w-20 h-20 object-cover rounded-lg"
                >
              </NuxtLink>
            </div>

            <!-- 商品信息 -->
            <div class="flex-1 min-w-0">
              <NuxtLink 
                :to="`/products/${item.product.id}`"
                class="block hover:text-primary-600 transition-colors"
              >
                <h3 class="font-medium text-gray-900 truncate">
                  {{ item.product.name }}
                </h3>
              </NuxtLink>
              
              <div class="flex items-center space-x-2 mt-1">
                <span class="text-lg font-semibold text-red-600">
                  ¥{{ item.product.price }}
                </span>
                <span 
                  v-if="item.product.originalPrice && item.product.originalPrice > item.product.price"
                  class="text-sm text-gray-500 line-through"
                >
                  ¥{{ item.product.originalPrice }}
                </span>
              </div>

              <div class="text-sm text-gray-500 mt-1">
                库存{{ item.product.stock }}件
              </div>
            </div>

            <!-- 数量控制 -->
            <div class="flex items-center space-x-3">
              <div class="flex items-center border rounded-lg">
                <UButton
                  variant="ghost"
                  size="sm"
                  @click="decreaseQuantity(item)"
                  :disabled="item.quantity <= 1"
                >
                  <Icon name="heroicons:minus" class="w-4 h-4" />
                </UButton>
                <input
                  :value="item.quantity"
                  @change="updateQuantity(item, $event)"
                  type="number"
                  min="1"
                  :max="item.product.stock"
                  class="w-16 text-center border-0 focus:ring-0"
                >
                <UButton
                  variant="ghost"
                  size="sm"
                  @click="increaseQuantity(item)"
                  :disabled="item.quantity >= item.product.stock"
                >
                  <Icon name="heroicons:plus" class="w-4 h-4" />
                </UButton>
              </div>

              <!-- 删除按钮 -->
              <UButton
                variant="ghost"
                size="sm"
                color="red"
                @click="cartStore.removeItem(item.id)"
              >
                <Icon name="heroicons:trash" class="w-4 h-4" />
              </UButton>
            </div>
          </div>
        </UCard>
      </div>

      <!-- 结算栏 -->
      <div class="sticky bottom-0 bg-white border-t p-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-600">
              已选择{{ cartStore.summary.selectedCount }}件商品
            </span>
            <div v-if="cartStore.summary.discountAmount > 0" class="text-sm text-green-600">
              已优惠 ¥{{ cartStore.summary.discountAmount.toFixed(2) }}
            </div>
          </div>

          <div class="flex items-center space-x-4">
            <div class="text-right">
              <div class="text-sm text-gray-500">
                合计：
                <span 
                  v-if="cartStore.summary.totalOriginalAmount > cartStore.summary.totalAmount"
                  class="line-through"
                >
                  ¥{{ cartStore.summary.totalOriginalAmount.toFixed(2) }}
                </span>
              </div>
              <div class="text-xl font-bold text-red-600">
                ¥{{ cartStore.summary.totalAmount.toFixed(2) }}
              </div>
            </div>

            <UButton
              size="lg"
              @click="handleCheckout"
              :disabled="!cartStore.hasSelected"
            >
              去结算({{ cartStore.summary.selectedCount }})
            </UButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { CartItem } from '~/types'

// 页面元信息
definePageMeta({
  middleware: 'auth'
})

// 页面SEO
useHead({
  title: '购物车 - 社交购物网站'
})

// 状态管理
const cartStore = useCartStore()
const toast = useToast()

// 数量操作
const increaseQuantity = async (item: CartItem) => {
  if (item.quantity < item.product.stock) {
    await cartStore.updateQuantity(item.id, item.quantity + 1)
  }
}

const decreaseQuantity = async (item: CartItem) => {
  if (item.quantity > 1) {
    await cartStore.updateQuantity(item.id, item.quantity - 1)
  }
}

const updateQuantity = async (item: CartItem, event: Event) => {
  const target = event.target as HTMLInputElement
  const newQuantity = parseInt(target.value) || 1
  
  if (newQuantity !== item.quantity) {
    await cartStore.updateQuantity(item.id, Math.min(newQuantity, item.product.stock))
  }
}

// 清空购物车
const handleClearCart = async () => {
  if (confirm('确定要清空购物车吗？')) {
    await cartStore.clearCart()
  }
}

// 去结算
const handleCheckout = () => {
  const selectedItems = cartStore.getSelectedItems()
  if (selectedItems.length === 0) {
    toast.add({
      title: '请选择要结算的商品',
      color: 'yellow'
    })
    return
  }

  navigateTo('/checkout')
}

// 页面加载时获取购物车数据
onMounted(() => {
  cartStore.fetchCart()
})
</script>
