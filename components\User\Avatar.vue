<template>
  <div class="relative inline-block">
    <img 
      :src="avatarUrl" 
      :alt="alt"
      :class="avatarClasses"
      @error="handleImageError"
    >
    
    <!-- 在线状态指示器 -->
    <div 
      v-if="showOnlineStatus && isOnline"
      class="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"
    />
    
    <!-- 角色徽章 -->
    <div 
      v-if="showRoleBadge && role && role !== 'USER'"
      class="absolute -top-1 -right-1"
    >
      <UBadge 
        :color="getRoleColor(role)" 
        variant="solid"
        size="xs"
      >
        {{ getRoleText(role) }}
      </UBadge>
    </div>
  </div>
</template>

<script setup lang="ts">
// 组件属性
interface Props {
  src?: string
  alt?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  shape?: 'circle' | 'square' | 'rounded'
  role?: 'USER' | 'MERCHANT' | 'ADMIN'
  isOnline?: boolean
  showOnlineStatus?: boolean
  showRoleBadge?: boolean
  fallbackSrc?: string
}

const props = withDefaults(defineProps<Props>(), {
  src: '',
  alt: '用户头像',
  size: 'md',
  shape: 'circle',
  role: 'USER',
  isOnline: false,
  showOnlineStatus: false,
  showRoleBadge: false,
  fallbackSrc: '/images/default-avatar.jpg'
})

// 响应式数据
const imageError = ref(false)

// 计算属性
const avatarUrl = computed(() => {
  if (imageError.value || !props.src) {
    return props.fallbackSrc
  }
  return props.src
})

const avatarClasses = computed(() => {
  const sizeClasses = {
    'xs': 'w-6 h-6',
    'sm': 'w-8 h-8',
    'md': 'w-10 h-10',
    'lg': 'w-12 h-12',
    'xl': 'w-16 h-16',
    '2xl': 'w-20 h-20'
  }
  
  const shapeClasses = {
    'circle': 'rounded-full',
    'square': 'rounded-none',
    'rounded': 'rounded-lg'
  }
  
  return [
    sizeClasses[props.size],
    shapeClasses[props.shape],
    'object-cover',
    'border-2 border-white shadow-sm'
  ].join(' ')
})

// 处理图片加载错误
const handleImageError = () => {
  imageError.value = true
}

// 获取角色颜色
const getRoleColor = (role: string) => {
  switch (role) {
    case 'ADMIN': return 'red'
    case 'MERCHANT': return 'blue'
    case 'USER': return 'green'
    default: return 'gray'
  }
}

// 获取角色文本
const getRoleText = (role: string) => {
  switch (role) {
    case 'ADMIN': return '管'
    case 'MERCHANT': return '商'
    case 'USER': return '用'
    default: return '?'
  }
}

// 重置图片错误状态
const resetImageError = () => {
  imageError.value = false
}

// 监听src变化，重置错误状态
watch(() => props.src, () => {
  resetImageError()
})

// 暴露方法给父组件
defineExpose({
  resetImageError
})
</script>
