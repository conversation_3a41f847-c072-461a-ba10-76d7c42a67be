import { getDatabaseHealth } from '~/utils/database'

/**
 * 健康检查接口
 * GET /api/health
 */
export default defineEventHandler(async () => {
  try {
    // 检查PostgreSQL数据库连接
    const dbHealth = await getDatabaseHealth()

    // 检查Redis连接
    // TODO: 实际的Redis连接检查
    const redisStatus = 'connected'

    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      services: {
        database: {
          status: dbHealth.status,
          responseTime: dbHealth.responseTime,
          statistics: dbHealth.statistics
        },
        redis: redisStatus
      },
      version: '1.0.0'
    }
  } catch (error) {
    console.error('健康检查失败:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Service Unavailable'
    })
  }
})
