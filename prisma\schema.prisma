// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id        Int      @id @default(autoincrement())
  username  String   @unique @db.VarChar(50)
  email     String   @unique @db.VarChar(100)
  phone     String?  @db.VarChar(20)
  password  String   @db.VarChar(255)
  avatar    String?  @db.VarChar(500)
  nickname  String?  @db.VarChar(50)
  bio       String?  @db.Text
  role      UserRole @default(USER)
  status    UserStatus @default(ACTIVE)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联关系
  products      Product[]
  orders        Order[]
  cartItems     CartItem[]
  addresses     ShippingAddress[]
  posts         Post[]
  comments      Comment[]
  likes         Like[]
  followers     Follow[] @relation("UserFollowers")
  following     Follow[] @relation("UserFollowing")
  sentMessages  Message[] @relation("MessageSender")
  receivedMessages Message[] @relation("MessageReceiver")
  reviews       Review[]
  favorites     Favorite[]
  userCoupons   UserCoupon[]
  searchLogs    SearchLog[]
  userLogs      UserLog[]

  @@map("users")
}

// 用户角色枚举
enum UserRole {
  USER
  MERCHANT
  ADMIN
}

// 用户状态枚举
enum UserStatus {
  ACTIVE
  INACTIVE
  BANNED
}

// 商品分类表
model Category {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(100)
  slug        String   @unique @db.VarChar(100)
  description String?  @db.Text
  image       String?  @db.VarChar(500)
  parentId    Int?     @map("parent_id")
  sort        Int      @default(0)
  status      CategoryStatus @default(ACTIVE)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 自关联
  parent   Category?  @relation("CategoryParent", fields: [parentId], references: [id])
  children Category[] @relation("CategoryParent")

  // 关联关系
  products Product[]

  @@map("categories")
}

enum CategoryStatus {
  ACTIVE
  INACTIVE
}

// 商品表
model Product {
  id            Int      @id @default(autoincrement())
  name          String   @db.VarChar(200)
  description   String   @db.Text
  price         Decimal  @db.Decimal(10, 2)
  originalPrice Decimal? @map("original_price") @db.Decimal(10, 2)
  images        String[] @db.VarChar(500)
  categoryId    Int      @map("category_id")
  merchantId    Int      @map("merchant_id")
  stock         Int      @default(0)
  sales         Int      @default(0)
  rating        Decimal  @default(0) @db.Decimal(3, 2)
  reviewCount   Int      @default(0) @map("review_count")
  status        ProductStatus @default(ACTIVE)
  tags          String[] @db.VarChar(50)
  specifications Json?
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // 关联关系
  category    Category    @relation(fields: [categoryId], references: [id])
  merchant    User        @relation(fields: [merchantId], references: [id])
  orderItems  OrderItem[]
  cartItems   CartItem[]
  reviews     Review[]
  favorites   Favorite[]
  posts       Post[]

  @@map("products")
}

enum ProductStatus {
  ACTIVE
  INACTIVE
  OUT_OF_STOCK
}

// 购物车表
model CartItem {
  id        Int      @id @default(autoincrement())
  userId    Int      @map("user_id")
  productId Int      @map("product_id")
  quantity  Int      @default(1)
  selected  Boolean  @default(true)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联关系
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("cart_items")
}

// 收货地址表
model ShippingAddress {
  id         Int     @id @default(autoincrement())
  userId     Int     @map("user_id")
  name       String  @db.VarChar(50)
  phone      String  @db.VarChar(20)
  province   String  @db.VarChar(50)
  city       String  @db.VarChar(50)
  district   String  @db.VarChar(50)
  address    String  @db.VarChar(200)
  postalCode String? @map("postal_code") @db.VarChar(10)
  isDefault  Boolean @default(false) @map("is_default")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // 关联关系
  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  orders Order[]

  @@map("shipping_addresses")
}

// 订单表
model Order {
  id              Int      @id @default(autoincrement())
  orderNo         String   @unique @map("order_no") @db.VarChar(50)
  userId          Int      @map("user_id")
  totalAmount     Decimal  @map("total_amount") @db.Decimal(10, 2)
  discountAmount  Decimal  @default(0) @map("discount_amount") @db.Decimal(10, 2)
  shippingAmount  Decimal  @default(0) @map("shipping_amount") @db.Decimal(10, 2)
  paymentAmount   Decimal  @map("payment_amount") @db.Decimal(10, 2)
  status          OrderStatus @default(PENDING)
  paymentStatus   PaymentStatus @default(PENDING) @map("payment_status")
  paymentMethod   PaymentMethod? @map("payment_method")
  shippingAddressId Int    @map("shipping_address_id")
  remark          String?  @db.Text
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // 关联关系
  user            User            @relation(fields: [userId], references: [id])
  shippingAddress ShippingAddress @relation(fields: [shippingAddressId], references: [id])
  items           OrderItem[]
  payments        Payment[]
  coupons         OrderCoupon[]

  @@map("orders")
}

enum OrderStatus {
  PENDING
  PAID
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}

enum PaymentMethod {
  ALIPAY
  WECHAT
  BANK_CARD
}

// 订单项表
model OrderItem {
  id           Int     @id @default(autoincrement())
  orderId      Int     @map("order_id")
  productId    Int     @map("product_id")
  productName  String  @map("product_name") @db.VarChar(200)
  productImage String  @map("product_image") @db.VarChar(500)
  price        Decimal @db.Decimal(10, 2)
  quantity     Int
  totalAmount  Decimal @map("total_amount") @db.Decimal(10, 2)
  createdAt    DateTime @default(now()) @map("created_at")

  // 关联关系
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

// 支付记录表
model Payment {
  id            Int           @id @default(autoincrement())
  orderId       Int           @map("order_id")
  paymentNo     String        @unique @map("payment_no") @db.VarChar(50)
  amount        Decimal       @db.Decimal(10, 2)
  method        PaymentMethod
  status        PaymentStatus @default(PENDING)
  transactionId String?       @map("transaction_id") @db.VarChar(100)
  paidAt        DateTime?     @map("paid_at")
  createdAt     DateTime      @default(now()) @map("created_at")
  updatedAt     DateTime      @updatedAt @map("updated_at")

  // 关联关系
  order Order @relation(fields: [orderId], references: [id])

  @@map("payments")
}

// 商品评价表
model Review {
  id        Int      @id @default(autoincrement())
  userId    Int      @map("user_id")
  productId Int      @map("product_id")
  orderId   Int?     @map("order_id")
  rating    Int      @db.SmallInt
  content   String   @db.Text
  images    String[] @db.VarChar(500)
  status    ReviewStatus @default(PUBLISHED)
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联关系
  user    User    @relation(fields: [userId], references: [id])
  product Product @relation(fields: [productId], references: [id])

  @@unique([userId, productId, orderId])
  @@map("reviews")
}

enum ReviewStatus {
  PUBLISHED
  HIDDEN
  DELETED
}

// 商品收藏表
model Favorite {
  id        Int      @id @default(autoincrement())
  userId    Int      @map("user_id")
  productId Int      @map("product_id")
  createdAt DateTime @default(now()) @map("created_at")

  // 关联关系
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("favorites")
}

// 社交动态表
model Post {
  id           Int        @id @default(autoincrement())
  userId       Int        @map("user_id")
  content      String     @db.Text
  images       String[]   @db.VarChar(500)
  type         PostType   @default(TEXT)
  productId    Int?       @map("product_id")
  likesCount   Int        @default(0) @map("likes_count")
  commentsCount Int       @default(0) @map("comments_count")
  sharesCount  Int        @default(0) @map("shares_count")
  status       PostStatus @default(PUBLISHED)
  createdAt    DateTime   @default(now()) @map("created_at")
  updatedAt    DateTime   @updatedAt @map("updated_at")

  // 关联关系
  user     User      @relation(fields: [userId], references: [id])
  product  Product?  @relation(fields: [productId], references: [id])
  comments Comment[]
  likes    Like[]

  @@map("posts")
}

enum PostType {
  TEXT
  IMAGE
  PRODUCT_SHARE
}

enum PostStatus {
  PUBLISHED
  DRAFT
  DELETED
}

// 评论表
model Comment {
  id        Int      @id @default(autoincrement())
  postId    Int      @map("post_id")
  userId    Int      @map("user_id")
  content   String   @db.Text
  parentId  Int?     @map("parent_id")
  likesCount Int     @default(0) @map("likes_count")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联关系
  post     Post      @relation(fields: [postId], references: [id], onDelete: Cascade)
  user     User      @relation(fields: [userId], references: [id])
  parent   Comment?  @relation("CommentReplies", fields: [parentId], references: [id])
  replies  Comment[] @relation("CommentReplies")
  likes    Like[]

  @@map("comments")
}

// 点赞表
model Like {
  id        Int      @id @default(autoincrement())
  userId    Int      @map("user_id")
  postId    Int?     @map("post_id")
  commentId Int?     @map("comment_id")
  createdAt DateTime @default(now()) @map("created_at")

  // 关联关系
  user    User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  post    Post?    @relation(fields: [postId], references: [id], onDelete: Cascade)
  comment Comment? @relation(fields: [commentId], references: [id], onDelete: Cascade)

  @@unique([userId, postId])
  @@unique([userId, commentId])
  @@map("likes")
}

// 关注关系表
model Follow {
  id          Int      @id @default(autoincrement())
  followerId  Int      @map("follower_id")
  followingId Int      @map("following_id")
  createdAt   DateTime @default(now()) @map("created_at")

  // 关联关系
  follower  User @relation("UserFollowers", fields: [followerId], references: [id], onDelete: Cascade)
  following User @relation("UserFollowing", fields: [followingId], references: [id], onDelete: Cascade)

  @@unique([followerId, followingId])
  @@map("follows")
}

// 私信表
model Message {
  id         Int         @id @default(autoincrement())
  senderId   Int         @map("sender_id")
  receiverId Int         @map("receiver_id")
  content    String      @db.Text
  type       MessageType @default(TEXT)
  isRead     Boolean     @default(false) @map("is_read")
  createdAt  DateTime    @default(now()) @map("created_at")
  updatedAt  DateTime    @updatedAt @map("updated_at")

  // 关联关系
  sender   User @relation("MessageSender", fields: [senderId], references: [id])
  receiver User @relation("MessageReceiver", fields: [receiverId], references: [id])

  @@map("messages")
}

enum MessageType {
  TEXT
  IMAGE
  SYSTEM
}

// 优惠券表
model Coupon {
  id                Int           @id @default(autoincrement())
  name              String        @db.VarChar(100)
  description       String?       @db.Text
  type              CouponType    @default(SHARED)
  discountType      DiscountType  @default(FIXED)
  discountValue     Decimal       @map("discount_value") @db.Decimal(10, 2)
  maxDiscountAmount Decimal?      @map("max_discount_amount") @db.Decimal(10, 2)
  minOrderAmount    Decimal       @default(0) @map("min_order_amount") @db.Decimal(10, 2)
  totalCount        Int           @map("total_count")
  usedCount         Int           @default(0) @map("used_count")
  perUserLimit      Int           @default(1) @map("per_user_limit")
  applicableScope   ApplicableScope @default(ALL) @map("applicable_scope")
  applicableIds     Int[]         @map("applicable_ids")
  startDate         DateTime      @map("start_date")
  endDate           DateTime      @map("end_date")
  status            CouponStatus  @default(ACTIVE)
  createdAt         DateTime      @default(now()) @map("created_at")
  updatedAt         DateTime      @updatedAt @map("updated_at")

  // 关联关系
  userCoupons UserCoupon[]
  orderCoupons OrderCoupon[]

  @@map("coupons")
}

// 优惠券类型枚举
enum CouponType {
  SHARED    // 同享券，可以与其他同享券叠加使用
  EXCLUSIVE // 互斥券，不能与其他券同时使用
}

// 折扣类型枚举
enum DiscountType {
  FIXED      // 固定金额
  PERCENTAGE // 百分比折扣
}

// 适用范围枚举
enum ApplicableScope {
  ALL        // 全场通用
  CATEGORY   // 指定分类
  PRODUCT    // 指定商品
  MERCHANT   // 指定商家
}

// 优惠券状态枚举
enum CouponStatus {
  ACTIVE   // 活跃
  INACTIVE // 停用
  EXPIRED  // 已过期
}

// 用户优惠券表
model UserCoupon {
  id        Int               @id @default(autoincrement())
  userId    Int               @map("user_id")
  couponId  Int               @map("coupon_id")
  status    UserCouponStatus  @default(UNUSED)
  usedAt    DateTime?         @map("used_at")
  expiresAt DateTime          @map("expires_at")
  createdAt DateTime          @default(now()) @map("created_at")

  // 关联关系
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  coupon       Coupon        @relation(fields: [couponId], references: [id], onDelete: Cascade)
  orderCoupons OrderCoupon[]

  @@unique([userId, couponId])
  @@map("user_coupons")
}

// 用户优惠券状态枚举
enum UserCouponStatus {
  UNUSED  // 未使用
  USED    // 已使用
  EXPIRED // 已过期
}

// 订单优惠券表
model OrderCoupon {
  id             Int     @id @default(autoincrement())
  orderId        Int     @map("order_id")
  userCouponId   Int     @map("user_coupon_id")
  couponId       Int     @map("coupon_id")
  discountAmount Decimal @map("discount_amount") @db.Decimal(10, 2)
  createdAt      DateTime @default(now()) @map("created_at")

  // 关联关系
  order      Order      @relation(fields: [orderId], references: [id], onDelete: Cascade)
  userCoupon UserCoupon @relation(fields: [userCouponId], references: [id])
  coupon     Coupon     @relation(fields: [couponId], references: [id])

  @@map("order_coupons")
}

// 搜索日志表
model SearchLog {
  id          Int      @id @default(autoincrement())
  keyword     String   @db.VarChar(255)
  resultCount Int      @map("result_count")
  userId      Int?     @map("user_id")
  userAgent   String?  @map("user_agent") @db.Text
  ipAddress   String?  @map("ip_address") @db.VarChar(45)
  filters     Json?    // 搜索筛选条件
  createdAt   DateTime @default(now()) @map("created_at")

  // 关联关系
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([keyword])
  @@index([userId])
  @@index([createdAt])
  @@map("search_logs")
}

// 用户操作日志表
model UserLog {
  id        Int           @id @default(autoincrement())
  userId    Int?          @map("user_id")
  action    UserLogAction
  resource  String?       @db.VarChar(100) // 操作的资源类型
  resourceId Int?         @map("resource_id") // 操作的资源ID
  details   Json?         // 操作详情
  userAgent String?       @map("user_agent") @db.Text
  ipAddress String?       @map("ip_address") @db.VarChar(45)
  createdAt DateTime      @default(now()) @map("created_at")

  // 关联关系
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([action])
  @@index([resource, resourceId])
  @@index([createdAt])
  @@map("user_logs")
}

// 用户操作类型枚举
enum UserLogAction {
  LOGIN           // 登录
  LOGOUT          // 登出
  REGISTER        // 注册
  PASSWORD_CHANGE // 修改密码
  PROFILE_UPDATE  // 更新个人信息
  PRODUCT_VIEW    // 查看商品
  PRODUCT_SEARCH  // 搜索商品
  ORDER_CREATE    // 创建订单
  ORDER_PAY       // 支付订单
  ORDER_CANCEL    // 取消订单
  CART_ADD        // 添加到购物车
  CART_REMOVE     // 从购物车移除
  FAVORITE_ADD    // 添加收藏
  FAVORITE_REMOVE // 移除收藏
  REVIEW_CREATE   // 创建评价
  COUPON_CLAIM    // 领取优惠券
  COUPON_USE      // 使用优惠券
  POST_CREATE     // 创建动态
  POST_LIKE       // 点赞动态
  COMMENT_CREATE  // 创建评论
  FOLLOW_USER     // 关注用户
  UNFOLLOW_USER   // 取消关注
}
