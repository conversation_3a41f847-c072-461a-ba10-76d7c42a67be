/**
 * 获取指定用户信息
 * GET /api/users/:id
 */

export default defineApiHandler(async (event) => {
  // 获取用户ID参数
  const userId = parseInt(getRouterParam(event, 'id') || '0')
  if (!userId || isNaN(userId)) {
    throw new ValidationError('用户ID格式不正确')
  }

  // 查询用户信息
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      id: true,
      username: true,
      avatar: true,
      nickname: true,
      bio: true,
      role: true,
      status: true,
      createdAt: true,
      // 统计信息
      _count: {
        select: {
          posts: true,
          followers: true,
          following: true,
          products: {
            where: { status: 'ACTIVE' }
          }
        }
      }
    }
  })

  if (!user) {
    throw new NotFoundError('用户不存在')
  }

  // 检查用户状态
  if (user.status !== 'ACTIVE') {
    throw new NotFoundError('用户不存在')
  }

  // 检查是否关注了该用户（如果当前用户已登录）
  let isFollowing = false
  const currentUserId = event.context.user?.id
  if (currentUserId && currentUserId !== userId) {
    const followRelation = await prisma.follow.findUnique({
      where: {
        followerId_followingId: {
          followerId: currentUserId,
          followingId: userId
        }
      }
    })
    isFollowing = !!followRelation
  }

  // 格式化返回数据
  const userProfile = {
    ...user,
    postsCount: user._count.posts,
    followersCount: user._count.followers,
    followingCount: user._count.following,
    productsCount: user._count.products,
    isFollowing
  }

  // 移除_count字段
  delete (userProfile as any)._count

  return createSuccessResponse(userProfile, '获取用户信息成功')
})
